# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides
# 
# Translators:
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-22 18:36+0000\n"
"PO-Revision-Date: 2023-10-26 23:10+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_engaged_count
msgid "# Active Attendees"
msgstr "# Asistentes activos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "# Attendees"
msgstr "# Asistentes"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "# Completed"
msgstr "# Completados"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_completed_count
msgid "# Completed Attendees"
msgstr "# Asistentes completados"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completed_slides_count
msgid "# Completed Contents"
msgstr "# Contenido completado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_count
msgid "# Enrolled Attendees"
msgstr "# Asistentes inscritos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_all_count
msgid "# Enrolled or Invited Attendees"
msgstr "# Asistentes inscritos o invitados"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_invited_count
msgid "# Invited Attendees"
msgstr "# Asistentes invitados"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "# Likes"
msgstr "# Me gusta"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "# Questions"
msgstr "# Preguntas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "# Quizz Attempts"
msgstr "# Intentos del cuestionario"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "# Total Attempts"
msgstr "# Intentos totales"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_views
msgid "# Total Views"
msgstr "# Vistas totales"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__count_views
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "# Views"
msgstr "# Vistas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_count
msgid "# of Embeds"
msgstr "# de incrustaciones"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__public_views
msgid "# of Public Views"
msgstr "# de vistas públicas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_views
msgid "# of Website Views"
msgstr "# de vistas del sitio web"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completion
msgid "% Completed Contents"
msgstr "% de contenido completado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "'. Showing results for '"
msgstr "\". Mostrando resultados para \""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_slides_list.js:0
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "(empty)"
msgstr "(vacío)"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ". This way, they will be secured."
msgstr ". De esta forma, estarán asegurados."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_5
msgid "3 Main Methodologies"
msgstr "3 metodologías principales"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "<b>%s</b> is requesting access to this course."
msgstr "<b>%s</b> solicita acceso a este curso."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<b>(empty)</b>"
msgstr "<b>(vacío)</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<b>Order by</b>"
msgstr "<b>Ordenar por</b>"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"<b>Save & Publish</b> your lesson to make it available to your attendees."
msgstr ""
"<b>Guarde y publique</b> su lección para que esté disponible para sus "
"asistentes."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "<b>Save</b> your question."
msgstr "<b>Guarde</b> su pregunta."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<b>Uncategorized</b>"
msgstr "<b>Sin categorizar</b>"

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_slide_channel_enroll
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br><br>\n"
"        You have been enrolled to a new course: <t t-out=\"object.channel_id.name or ''\">Basics of Gardening</t>.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hola<br><br>\n"
"        Se ha inscrito a un nuevo curso: <t t-out=\"object.channel_id.name or ''\">Fundamentos de jardinería</t>.\n"
"    </p>\n"
"</div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_slide_channel_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br><br>\n"
"        You have been invited to check out this course: <t t-out=\"object.channel_id.name or ''\">Basics of Gardening</t>.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hola<br><br>\n"
"        Ha sido invitado a revisar este curso: <t t-out=\"object.channel_id.name or ''\">Fundamentos de jardinería</t>.\n"
"    </p>\n"
"</div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_channel_completed
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        <p style=\"margin: 0px;\">Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,</p><br>\n"
"                        <p><b>Congratulations!</b></p>\n"
"                        <p>You've completed the course <b t-out=\"object.channel_id.name or ''\">Basics of Gardening</b></p>\n"
"                        <p>Check out the other available courses.</p><br>\n"
"\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"/slides/all\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                Explore courses\n"
"                            </a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"object.channel_id.user_id.signature\">\n"
"                            <br>\n"
"                            <t t-out=\"object.channel_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        <p style=\"margin: 0px;\">Hola <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,</p><br>\n"
"                        <p><b>¡Enhorabuena!</b></p>\n"
"                        <p>He terminado el curso <b t-out=\"object.channel_id.name or ''\">Fundamientos de jardinería</b></p>\n"
"                        <p>Eche un vistazo a los otros cursos disponibles.</p><br>\n"
"\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"/slides/all\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                Explorar cursos\n"
"                            </a>\n"
"                        </div>\n"
"                        ¡Disfrute de este contenido!\n"
"                        <t t-if=\"object.channel_id.user_id.signature\">\n"
"                            <br>\n"
"                            <t t-out=\"object.channel_id.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_channel_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br><br>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> shared the <strong t-out=\"object.name or ''\">document</strong> with you!\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.channel/{{ object.id }}/image_256\" style=\"height:auto; width:150px; margin: 16px;\">\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px;                                 text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                View <strong t-out=\"object.name or ''\">Document</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br>\n"
"                            <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hola<br><br>\n"
"                        ¡<t t-out=\"user.name or ''\">Mitchell Admin</t> compartió el <strong t-out=\"object.name or ''\">documento</strong> con usted!\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.channel/{{ object.id }}/image_256\" style=\"height:auto; width:150px; margin: 16px;\">\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px;                                 text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                Ver <strong t-out=\"object.name or ''\">Documento</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br>\n"
"                            <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br><br>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> shared the <t t-out=\"object.slide_category or ''\">document</t> <strong t-out=\"object.name or ''\">Trees</strong> with you!\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\">\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View <strong t-out=\"object.name or ''\">Trees</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br>\n"
"                            <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hola<br><br>\n"
"                        ¡<t t-out=\"user.name or ''\">Mitchell Admin</t> ha compartido el <t t-out=\"object.slide_category or ''\">documento</t> <strong t-out=\"object.name or ''\">Árboles</strong> con usted!\n"
"                        <div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\">\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">Ver <strong t-out=\"object.name or ''\">Árboles</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br>\n"
"                            <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_published
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br><br>\n"
"                        There is something new in the course <strong t-out=\"object.channel_id.name or ''\">Trees, Wood and Gardens</strong> you are following:<br><br>\n"
"                        <center><strong t-out=\"object.name or ''\">Trees</strong></center>\n"
"                        <t t-if=\"object.image_1024\">\n"
"                            <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                                <a t-att-href=\"object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\">\n"
"                            </a>\n"
"                        </div>\n"
"                        </t>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View content</a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br>\n"
"                            <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hola<br><br>\n"
"                        Hay nuevo contenido en el curso <strong t-out=\"object.channel_id.name or ''\">Árboles, madera y jardines</strong> que usted sigue:<br><br>\n"
"                        <center><strong t-out=\"object.name or ''\">Árboles</strong></center>\n"
"                        <t t-if=\"object.image_1024\">\n"
"                            <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                                <a t-att-href=\"object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\">\n"
"                            </a>\n"
"                        </div>\n"
"                        </t>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">Ver contenido</a>\n"
"                        </div>\n"
"                        ¡Disfrute de este contenido exclusivo!\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br>\n"
"                            <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "<i class=\"fa fa-arrow-right\"/> Start Learning"
msgstr "<i class=\"fa fa-arrow-right\"/> Empezar a aprender"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-bar-chart\"/> Statistics"
msgstr "<i class=\"fa fa-bar-chart\"/> Estadísticas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Lessons</span>"
msgstr ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Lecciones</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<i class=\"fa fa-check me-1\"/>Completed"
msgstr "<i class=\"fa fa-check me-1\"/>Completado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "<i class=\"fa fa-check\"/> Completed"
msgstr "<i class=\"fa fa-check\"/> Completado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-circle-o-notch fa-spin me-2\"/><b>Loading...</b>"
msgstr "<i class=\"fa fa-circle-o-notch fa-spin me-2\"/><b>Cargando...</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "<i class=\"fa fa-clipboard\"/> Copy Embed Code"
msgstr "<i class=\"fa fa-clipboard\"/> Copiar código de incrustación"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
msgid "<i class=\"fa fa-clipboard\"/> Copy Link"
msgstr "<i class=\"fa fa-clipboard\"/> Copiar enlace"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-clock-o me-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"Duration\"/>"
msgstr ""
"<i class=\"fa fa-clock-o me-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"Duration\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-cloud-upload me-1\"/>Add Content"
msgstr "<i class=\"fa fa-cloud-upload me-1\"/>Añadir contenido"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-comments\"/> Comments ("
msgstr "<i class=\"fa fa-comments\"/> Comentarios ("

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-desktop me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-desktop me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">Pantalla completa</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<i class=\"fa fa-envelope\"/> Send Email"
msgstr "<i class=\"fa fa-envelope\"/> Enviar correo electrónico"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-eraser me-1\"/>Clear filters"
msgstr "<i class=\"fa fa-eraser me-1\"/>Eliminar filtros"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "<i class=\"fa fa-eraser\"/> Clear filters"
msgstr "<i class=\"fa fa-eraser\"/> Eliminar filtros"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide_forbidden
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/> This document is private."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/> Este documento es privado."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-eye me-2\" aria-label=\"Views\" role=\"img\" title=\"Views\"/>"
msgstr "<i class=\"fa fa-eye me-2\" aria-label=\"Views\" role=\"img\" title=\"Views\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-code-o me-2\" aria-label=\"article\" role=\"img\" "
"title=\"Article\"/>"
msgstr ""
"<i class=\"fa fa-file-code-o me-2\" aria-label=\"article\" role=\"img\" "
"title=\"Article\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-image-o me-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"Infographic\"/>"
msgstr ""
"<i class=\"fa fa-file-image-o me-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"Infographic\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-pdf-o me-2\" aria-label=\"Document\" role=\"img\" "
"title=\"Document\"/>"
msgstr ""
"<i class=\"fa fa-file-pdf-o me-2\" aria-label=\"Document\" role=\"img\" "
"title=\"Document\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-video-o me-2\" aria-label=\"Video\" role=\"img\" "
"title=\"Video\"/>"
msgstr ""
"<i class=\"fa fa-file-video-o me-2\" aria-label=\"Video\" role=\"img\" "
"title=\"Video\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-flag me-2\" aria-label=\"Quiz\" role=\"img\" title=\"Quiz\"/>"
msgstr "<i class=\"fa fa-flag me-2\" aria-label=\"Quiz\" role=\"img\" title=\"Quiz\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<i class=\"fa fa-flag text-warning\"/> Quiz"
msgstr "<i class=\"fa fa-flag text-warning\"/>Cuestionario"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen_sidebar_category
msgid "<i class=\"fa fa-flag-checkered text-warning\"/>Quiz"
msgstr "<i class=\"fa fa-flag-checkered text-warning\"/>Cuestionario"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-folder-o me-1\"/><span>Add Section</span>"
msgstr "<i class=\"fa fa-folder-o me-1\"/><span>Añadir sección</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-folder-o me-1\"/>Add a section"
msgstr "<i class=\"fa fa-folder-o me-1\"/>Añadir una sección"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "<i class=\"fa fa-graduation-cap me-1\"/>All courses"
msgstr "<i class=\"fa fa-graduation-cap me-1\"/>Todos los cursos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-home\"/> About"
msgstr "<i class=\"fa fa-home\"/> Sobre"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-home\"/> Course"
msgstr "<i class=\"fa fa-home\"/>Curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block ms-1\">Back "
"to course</span>"
msgstr ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Volver al curso</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid ""
"<i class=\"fa fa-lock\"/>\n"
"                        <span>Course Locked</span>"
msgstr ""
"<i class=\"fa fa-lock\"/>\n"
"                        <span>Curso bloqueado</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid ""
"<i class=\"fa fa-plus me-1\"/> <span class=\"d-none d-md-inline-block\">Add "
"Content</span>"
msgstr ""
"<i class=\"fa fa-plus me-1\"/> <span class=\"d-none d-md-inline-"
"block\">Añadir contenido</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-plus me-1\"/><span>Add Content</span>"
msgstr "<i class=\"fa fa-plus me-1\"/><span>Añadir contenido</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>Add Question</span>"
msgstr ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>Añadir pregunta</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>Add Quiz</span>"
msgstr ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>Añadir cuestionario</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-question me-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"Number of Questions\"/>"
msgstr ""
"<i class=\"fa fa-question me-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"Number of Questions\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-share-alt me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">Share</span>"
msgstr ""
"<i class=\"fa fa-share-alt me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">Compartir</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<i class=\"fa fa-share-alt\" aria-label=\"Share\" title=\"Share\"/>\n"
"                                            Share"
msgstr ""
"<i class=\"fa fa-share-alt\" aria-label=\"Share\" title=\"Share\"/>\n"
"                                            Compartir"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-share-alt\"/>\n"
"                        <span class=\"d-none d-md-inline-block ms-2\">Share</span>"
msgstr ""
"<i class=\"fa fa-share-alt\"/>\n"
"                        <span class=\"d-none d-md-inline-block ms-2\">Compartir</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "<i class=\"fa fa-share-alt\"/> Share"
msgstr "<i class=\"fa fa-share-alt\"/> Compartir"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Exit Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Salir de pantalla completa</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid ""
"<i class=\"fa fa-tag me-2 text-muted\"/>\n"
"                      My Courses"
msgstr ""
"<i class=\"fa fa-tag me-2 text-muted\"/>\n"
"                      Mis cursos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "<i class=\"oi oi-arrow-right me-1\"/>All Courses"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>Todos los cursos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"oi oi-chevron-left me-2\"/> <span class=\"d-none d-sm-inline-"
"block\">Prev</span>"
msgstr ""
"<i class=\"oi oi-chevron-left me-2\"/> <span class=\"d-none d-sm-inline-"
"block\">Anterior</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid ""
"<small class=\"text-success\">\n"
"                        Request already sent\n"
"                    </small>"
msgstr ""
"<small class=\"text-success\">\n"
"                        Solicitud ya enviada\n"
"                    </small>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid ""
"<small><span class=\"badge rounded-pill text-bg-success fw-normal\"><i "
"class=\"fa fa-check\"/> Completed</span></small>"
msgstr ""
"<small><span class=\"badge rounded-pill text-bg-success fw-normal\"><i "
"class=\"fa fa-check\"/> Completado</span></small>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge fw-bold px-2 py-1 m-1 text-bg-warning\">\n"
"                            <i class=\"fa fa-fw fa-flag\"/> 10 xp\n"
"                        </span>"
msgstr ""
"<span class=\"badge fw-bold px-2 py-1 m-1 text-bg-warning\">\n"
"                            <i class=\"fa fa-fw fa-flag\"/> 10 xp\n"
"                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid ""
"<span class=\"badge rounded-pill text-bg-success pull-right py-1 px-2\"><i "
"class=\"fa fa-check\"/> Completed</span>"
msgstr ""
"<span class=\"badge rounded-pill text-bg-success pull-right py-1 px-2\"><i "
"class=\"fa fa-check\"/> Completado</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge text-bg-info badge-arrow-right fw-normal px-2 py-1 "
"m-1\">New</span>"
msgstr ""
"<span class=\"badge text-bg-info badge-arrow-right fw-normal px-2 py-1 "
"m-1\">Nuevo</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge text-bg-light badge-hide border fw-normal px-2 py-1 "
"m-1\">Add Quiz</span>"
msgstr ""
"<span class=\"badge text-bg-light badge-hide border fw-normal px-2 py-1 "
"m-1\">Añadir cuestionario</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge text-bg-success fw-normal px-2 py-1 "
"m-1\"><span>Preview</span></span>"
msgstr ""
"<span class=\"badge text-bg-success fw-normal px-2 py-1 m-1\"><span>Vista "
"previa</span></span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge text-bg-success fw-normal px-2 py-1 m-1\">Preview</span>"
msgstr ""
"<span class=\"badge text-bg-success fw-normal px-2 py-1 m-1\">Vista "
"previa</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<span class=\"d-none d-sm-inline-block\">Next</span> <i class=\"oi oi-"
"chevron-right ms-2\"/>"
msgstr ""
"<span class=\"d-none d-sm-inline-block\">Siguiente</span> <i class=\"oi oi-"
"chevron-right ms-2\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "<span class=\"fw-bold text-muted me-2\">Current rank:</span>"
msgstr "<span class=\"fw-bold text-muted me-2\">Rango actual:</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid "<span class=\"fw-normal\">Last update:</span>"
msgstr "<span class=\"fw-normal\">Última actualización:</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "<span class=\"input-group-text\">Start at Page</span>"
msgstr "<span class=\"input-group-text\">Empezar en la página</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid ""
"<span class=\"ms-1\">Lessons</span>\n"
"                                <span class=\"ms-1\">·</span>"
msgstr ""
"<span class=\"ms-1\">Lecciones</span>\n"
"                                <span class=\"ms-1\">·</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Slides</span>\n"
"                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">Diapositivas</span>\n"
"                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "<span class=\"o_stat_text\">Attendees</span>"
msgstr "<span class=\"o_stat_text\">Asistentes</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_partner_view_form
msgid "<span class=\"o_stat_text\">Courses</span>"
msgstr "<span class=\"o_stat_text\">Cursos</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "<span class=\"o_stat_text\">Embed Views</span>"
msgstr "<span class=\"o_stat_text\">Incrustar vistas</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card_information_arrow
msgid "<span class=\"o_wslides_arrow\">New Content</span>"
msgstr "<span class=\"o_wslides_arrow\">Nuevo contenido</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"oi oi-arrow-right\"/>\n"
"                                Create a Google Project and Get a Key"
msgstr ""
"<span class=\"oi oi-arrow-right\"/>\n"
"                                Crear un proyecto de Google y obtener una clave"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training
msgid "<span class=\"p-2\">Course content</span>"
msgstr "<span class=\"p-2\">Contenido del curso</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "<span class=\"text-500 mx-2\">•</span>"
msgstr "<span class=\"text-500 mx-2\">•</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr "<span class=\"text-bg-danger\">Archivado</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-bg-success\">Published</span>"
msgstr "<span class=\"text-bg-success\">Publicado</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<span class=\"text-muted fw-bold col-4 col-md-3\">\n"
"                        Additional Resources\n"
"                    </span>"
msgstr ""
"<span class=\"text-muted fw-bold col-4 col-md-3\">\n"
"                        Recursos adicionales\n"
"                    </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span class=\"text-muted fw-bold col-4 col-md-3\">External sources</span>"
msgstr "<span class=\"text-muted fw-bold col-4 col-md-3\">Fuentes externas</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span class=\"text-muted fw-bold me-3\">Rating</span>"
msgstr "<span class=\"text-muted fw-bold me-3\">Calificación</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Common tasks for a computer scientist</span>"
msgstr "<span class=\"text-muted\">Tareas comunes para un informático</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Invited</span>"
msgstr "<span class=\"text-muted\">Invitado</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Ongoing</span>"
msgstr "<span class=\"text-muted\">En progreso</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Parts of computer science</span>"
msgstr "<span class=\"text-muted\">Partes de la informática</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted\">Total</span>"
msgstr "<span class=\"text-muted\">Total</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"first\" class=\"me-1 me-sm-2\" title=\"First slide\" aria-label=\"First slide\" role=\"button\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"Previous slide\" aria-label=\"Previous slide\" role=\"button\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"Next slide\" aria-label=\"Next slide\" role=\"button\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"Last slide\" aria-label=\"Last slide\" role=\"button\"><i class=\"fa fa-step-forward\"/></span>"
msgstr ""
"<span id=\"first\" class=\"me-1 me-sm-2\" title=\"First slide\" aria-label=\"First slide\" role=\"button\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"Previous slide\" aria-label=\"Previous slide\" role=\"button\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"Next slide\" aria-label=\"Next slide\" role=\"button\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"Last slide\" aria-label=\"Last slide\" role=\"button\"><i class=\"fa fa-step-forward\"/></span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"fullscreen\" class=\"ms-1 ms-sm-2\" title=\"View fullscreen\" aria-label=\"Fullscreen\" role=\"button\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"
msgstr ""
"<span id=\"fullscreen\" class=\"ms-1 ms-sm-2\" title=\"View fullscreen\" aria-label=\"Fullscreen\" role=\"button\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"zoomout\" class=\"d-inline ms-2 me-2\" title=\"Zoom out\" aria-label=\"Zoom out\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-inline\" title=\"Zoom in\" aria-label=\"Zoom in\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"
msgstr ""
"<span id=\"zoomout\" class=\"d-inline ms-2 me-2\" title=\"Zoom out\" aria-label=\"Zoom out\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-inline\" title=\"Zoom in\" aria-label=\"Zoom in\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span name=\"done_members_count_label\" class=\"text-muted\">Finished</span>"
msgstr ""
"<span name=\"done_members_count_label\" class=\"text-"
"muted\">Finalizado</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid ""
"<span name=\"members_completed_count_label\" "
"class=\"o_stat_text\">Finished</span>"
msgstr ""
"<span name=\"members_completed_count_label\" "
"class=\"o_stat_text\">Finalizado</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "<span> hours</span>"
msgstr "<span> horas</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<span>Add Tag</span>"
msgstr "<span>Añadir etiqueta</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Answering Questions</span>"
msgstr "<span>Respondiendo preguntas</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking Question</span>"
msgstr "<span>Preguntando</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking the right question</span>"
msgstr "<span>Haciendo la pregunta correcta</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span>Content only accessible to course attendees.</span>"
msgstr ""
"<span>El contenido solo está disponible para los asistentes del "
"curso.</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Logic</span>"
msgstr "<span>Lógica</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Mathematics</span>"
msgstr "<span>Matemáticas</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "<span>Preview</span>"
msgstr "<span>Vista previa</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Science</span>"
msgstr "<span>Ciencia</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span>XP</span>"
msgstr "<span>XP</span>"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_overview
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_report
msgid "<strong>Create a course</strong>"
msgstr "<strong>Crear un curso</strong>"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_partner_action_from_slide
msgid "<strong>No Attendee Yet!</strong>"
msgstr "<strong>¡Todavía no hay asistentes!</strong>"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action_report
msgid "<strong>No Attendees Yet!</strong>"
msgstr "<strong>¡Todavía no hay asistentes!</strong>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<strong>Sharing is caring!</strong> Email(s) sent."
msgstr ""
"<strong>¡Compartir es importante!</strong> Correos electrónicos enviados."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_1
msgid "A Mighty Forest from Ages"
msgstr "Un bosque poderoso desde hace siglos"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_0
msgid "A fruit"
msgstr "Una fruta"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"A good course has a structure. Pick a name for your first <b>Section</b>."
msgstr ""
"Un buen curso tiene una estructura. Póngale un nombre a su primera "
"<b>sección</b>."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_4
msgid "A little chat with Harry Potted"
msgstr "Una pequeña conversación con Harry Potted"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_2_gard2
msgid ""
"A lot of nice documentation: trees, wood, gardens. A gold mine for "
"references."
msgstr ""
"Mucha documentación buena: árboles, madera, jardines. Una mina de oro para "
"referencias."

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_channel_partner_channel_partner_uniq
msgid "A partner membership to a channel must be unique!"
msgstr "¡La afiliación del contacto al canal debe ser única!"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_partner_slide_partner_uniq
msgid "A partner membership to a slide must be unique!"
msgstr "¡La afiliación del contacto al canal debe ser única!"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_resource_check_file_type
msgid "A resource of type file cannot contain a link."
msgstr "Un recurso de tipo archivo no puede contener un enlace."

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_resource_check_url
msgid "A resource of type url must contain a link."
msgstr "Un recurso de tipo URL debe contener un enlace."

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_0
msgid "A shovel"
msgstr "Una pala"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_exclusion_html_content_and_url
msgid "A slide is either filled with a url or HTML content. Not both."
msgstr ""
"Una diapositiva debe contener una URL o contenido HTML, pero no ambos."

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_1
msgid "A spoon"
msgstr "Una cuchara"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_5
msgid "A summary of know-how: how and what."
msgstr "Un resumen sobre el conocimiento técnico: cómo y qué."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_0
msgid ""
"A summary of know-how: how and what. All the basics for this course about "
"gardening."
msgstr ""
"Un resumen sobre el conocimiento técnico: cómo y qué. Lo básico para este "
"curso sobre jardinería."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_0
msgid ""
"A summary of know-how: what are the main trees categories and how to "
"differentiate them."
msgstr ""
"Un resumen sobre el conocimiento técnico: cuáles son las principales "
"categorías de árboles y cómo diferenciarlas."

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_2
msgid "A table"
msgstr "Una mesa"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_tag_slide_tag_unique
msgid "A tag must be unique!"
msgstr "¡Una etiqueta debe ser única!"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_1
msgid "A vegetable"
msgstr "Una verdura"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "API Key"
msgstr "Clave API"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Access Granted"
msgstr "Acceso permitido"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_groups
msgid "Access Groups"
msgstr "Grupos de acceso"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Access Refused"
msgstr "Acceso rechazado"

#. module: website_slides
#: model:mail.activity.type,name:website_slides.mail_activity_data_access_request
msgid "Access Request"
msgstr "Solicitud de acceso"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_requested_access
msgid "Access Requested"
msgstr "Acceso solicitado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Access Rights"
msgstr "Permisos de acceso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "Accessed on"
msgstr "Accesado el"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
msgid "Achievements"
msgstr "Logros"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__active
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__active
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__active
msgid "Active"
msgstr "Activo"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__members_engaged_count
msgid "Active attendees include both 'joined' and 'ongoing' attendees."
msgstr ""
"Los asistentes activos incluyen tanto a los asistentes 'unidos' como a los "
"'en curso'.   "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Actividad de Excepción"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actvidad"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#, python-format
msgid "Add"
msgstr "Añadir"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Attendees"
msgstr "Añadir asistentes"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_comment
msgid "Add Comment"
msgstr "Añadir comentario"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#, python-format
msgid "Add Content"
msgstr "Añadir contenido"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_review
msgid "Add Review"
msgstr "Añadir reseña"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Section"
msgstr "Añadir sección"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Add Tag"
msgstr "Añadir etiqueta"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#, python-format
msgid "Add a section"
msgstr "Añadir una sección"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
#, python-format
msgid "Add a tag"
msgstr "Añadir una etiqueta"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Add an answer below this one"
msgstr "Añadir una respuesta debajo de esta"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Add comment on this answer"
msgstr "Añadir un comentario en esta respuesta"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Add contacts..."
msgstr "Añadir contactos..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Add quiz"
msgstr "Añadir cuestionario"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Add your content here..."
msgstr "Añada su contenido aquí..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Added On"
msgstr "Añadido el"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_ids
msgid "Additional Resource for this slide"
msgstr "Recurso adicional para esta diapositiva"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Additional Resources"
msgstr "Recursos adicionales"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_resource
msgid "Additional resource for a particular slide"
msgstr "Recurso adicional para esta diapositiva en particular"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_advanced
msgid "Advanced"
msgstr "Avanzado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_partner_all_ids
msgid "All Attendees Information"
msgstr "Información de todos los asistentes"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_overview
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "All Courses"
msgstr "Todos los cursos"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
#, python-format
msgid "All progress will be lost until you rejoin this course."
msgstr "Todo el progreso se perderá hasta que se vuelva a unir a este curso. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "All questions must be answered!"
msgstr "¡Se deben responder todas las preguntas!"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_question.py:0
#, python-format
msgid ""
"All questions must have at least one correct answer and one incorrect answer: \n"
"%s\n"
msgstr ""
"Todas las preguntas deben tener por lo menos una respuesta correcta y una incorrecta: \n"
"%s\n"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "All the courses you attend will appear here. <br/>"
msgstr "Todos los cursos a los que asista aparecerán aquí. <br/>"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_5_furn2
msgid "All you need to know about furniture creation."
msgstr "Todo lo que debe saber sobre creación de muebles."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__allow_comment
#: model:ir.model.fields,help:website_slides.field_slide_slide__channel_allow_comment
msgid ""
"Allow Attendees to like and comment your content and to submit reviews on "
"your course."
msgstr ""
"Permita a los asistentes dar me gusta, comentar su contenido y enviar "
"reseñas sobre su curso."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow Download"
msgstr "Permitir descarga"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_preview
msgid "Allow Preview"
msgstr "Permitir vista previa"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid "Allow Rating"
msgstr "Permitir calificación"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Allow Reviews"
msgstr "Permitir reseñas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__allow_comment
msgid "Allow rating on Course"
msgstr "Permitir calificación de curso"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow the user to download the content of the slide."
msgstr "Permita a los usuarios descargar el contenido de la diapositiva."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_allow_comment
msgid "Allows comment"
msgstr "Permite comentar"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Already Requested"
msgstr "Ya solicitado"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Already installing \"%s\"."
msgstr "Ya instalando \"%s\"."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Already member"
msgstr "Ya es un miembro"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Amazing!"
msgstr "¡Increíble!"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_2
msgid "And also bananas"
msgstr "Y también plátanos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__text_value
#: model:ir.model.fields,field_description:website_slides.field_slide_question__answer_ids
msgid "Answer"
msgstr "Respuesta"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Answers"
msgstr "Respuestas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "Aparece en"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
#, python-format
msgid "Archive"
msgstr "Archivar"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#, python-format
msgid "Archive Content"
msgstr "Archivar contenido"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Archived"
msgstr "Archivado"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Are you sure you want to archive this content?"
msgstr "¿Está seguro de que desea archivar este contenido? "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
#, python-format
msgid "Are you sure you want to delete this category?"
msgstr "¿Esta seguro de que desea eliminar esta categoría? "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Are you sure you want to delete this question:"
msgstr "¿Está seguro de que desea eliminar esta pregunta?: "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__article
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__article
#, python-format
msgid "Article"
msgstr "Artículo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_article
msgid "Articles"
msgstr "Artículos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_attachment_count
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__attachment_ids
msgid "Attachments"
msgstr "Archivos adjuntos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_avg
msgid "Attempts Avg"
msgstr "Promedio de intentos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_count
msgid "Attempts Count"
msgstr "Número de intentos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_form
msgid "Attendee"
msgstr "Asistente"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__member_status
msgid "Attendee Status"
msgstr "Estado de asistencia"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action_report
#: model:ir.actions.act_window,name:website_slides.slide_slide_partner_action_from_slide
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_ids
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_attendees
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_graph
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_pivot
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Attendees"
msgstr "Asistentes"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Attendees of %s"
msgstr "Asistentes en %s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_group_ids
msgid "Auto Enroll Groups"
msgstr "Inscribir grupos automáticamente"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg
msgid "Average Rating"
msgstr "Calificación promedio"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "Average Review"
msgstr "Reseña promedio"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "BUILDING BLOCKS DROPPED HERE WILL BE SHOWN ACROSS ALL LESSONS"
msgstr ""
"LOS BLOQUES DE CREACIÓN QUE SE DEJEN AQUÍ SE MOSTRARÁN EN TODOS LOS CURSOS"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Back"
msgstr "Volver"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Back to course"
msgstr "Volver al curso"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_default_background_image_url
msgid "Background image URL"
msgstr "URL de la imagen de fondo"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Badges"
msgstr "Insignias"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_basic
msgid "Basic"
msgstr "Básico"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_5_furn2
msgid "Basics of Furniture Creation"
msgstr "Fundamentos de la creación de muebles"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_0_gard_0
msgid "Basics of Gardening"
msgstr "Conceptos básicos de jardinería"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
#, python-format
msgid "Be notified when a new content is added."
msgstr "¿Desea que se le notifique cuando se añade contenido nuevo?"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__body_has_template_value
msgid "Body content is the same as the template"
msgstr "El contenido del cuerpo es igual al de la plantilla"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_comment
msgid "Can Comment"
msgstr "Puede comentar"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__can_edit_body
msgid "Can Edit Body"
msgstr "Puede editar el cuerpo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_self_mark_completed
msgid "Can Mark Completed"
msgstr "Puede marcar como completado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_self_mark_uncompleted
msgid "Can Mark Uncompleted"
msgstr "Puede marcar como no completado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_publish
msgid "Can Publish"
msgstr "Puede publicar"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_review
msgid "Can Review"
msgstr "Puede escribir reseñas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_upload
msgid "Can Upload"
msgstr "Puede subir"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_vote
msgid "Can Vote"
msgstr "Puede votar"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
#, python-format
msgid "Can not be marked as done"
msgstr "No se puede marcar como hecho"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
#, python-format
msgid "Can not be marked as not done"
msgstr "No se puede marcar como no hecho"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_carpenter
msgid "Carpenter"
msgstr "Carpintero"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Catchy Headline"
msgstr "Título llamativo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_category_ids
msgid "Categories"
msgstr "Categorías"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_category
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__slide_category
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Category"
msgstr "Categoría"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_1
msgid "Certification"
msgstr "Certificación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Certifications"
msgstr "Certificaciones"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_certification
#: model:gamification.goal.definition,name:website_slides.badge_data_certification_goal
msgid "Certified Knowledge"
msgstr "Conocimiento certificado"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Change video privacy settings"
msgstr "Cambiar ajustes de privacidad de vídeo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__channel_id
msgid "Channel"
msgstr "Canal"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_partner
msgid "Channel / Partners (Members)"
msgstr "Canal/contactos (miembros)"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_invite
msgid "Channel Invitation Wizard"
msgstr "Asistente de invitación al canal"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_channel_template_id
msgid "Channel Share Template"
msgstr "Plantilla para compartir el canal"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_channel_shared
msgid "Channel Shared"
msgstr "Canal compartido"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_type
msgid "Channel type"
msgstr "Tipo de canal"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag_group
msgid "Channel/Course Groups"
msgstr "Grupos del canal/curso"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag
msgid "Channel/Course Tag"
msgstr "Etiqueta del canal/curso"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__channel_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Channels"
msgstr "Canales"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_cheatsheet
msgid "CheatSheet"
msgstr "Ayuda memoria"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check Profile"
msgstr "Revisar perfil"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check answers"
msgstr "Revisar respuestas"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Check your answers"
msgstr "Revisar sus respuestas"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Choose a <b>File</b> on your computer."
msgstr "Elija un <b>archivo</b> de su ordenador."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Choose a PDF"
msgstr "Elegir un PDF"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid "Choose a layout"
msgstr "Elija un diseño"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Choose an Image"
msgstr "Elegir una imagen"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_3_furn0
msgid "Choose your wood!"
msgstr "¡Elija su madera!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Clear filters"
msgstr "Eliminar filtros"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Click <b>Save</b> to create it."
msgstr "Haga clic en <b>Guardar</b> para crearla."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Click here to get started"
msgstr "Haga clic aquí para comenzar"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Click here to start the course"
msgstr "Haga clic aquí para iniciar el curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Click on \"New\" in the top-right corner to write your first course."
msgstr ""
"Haga clic en \"Nuevo\" en la esquina superior derecha para escribir su "
"primer curso."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"Click on the \"Edit\" button in the top corner of the screen to edit your "
"slide content."
msgstr ""
"Haga clic en el botón de \"Editar\" en la esquina superior de la pantalla "
"para editar el contenido de su diapositiva."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Click on the <b>Save</b> button to create your first course."
msgstr "Haga clic en el botón <b>Guardar</b> para crear su primer curso."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Click on your <b>Course</b> to go back to the table of content."
msgstr "Haga clic en su <b>curso</b> para regresar al índice."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Color"
msgstr "Color"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__color
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__color
msgid "Color Index"
msgstr "Índice de Colores"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_colorful
msgid "Colorful"
msgstr "Colorido"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel
msgid "Come back later to check the feedbacks given by your Attendees."
msgstr "Vuelva más tarde para revisar la retroalimentación de sus asistentes."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid "Come back later to oversee how well your Attendees are doing."
msgstr "Vuelva más tarde para revisar qué tan bien le va a sus asistentes."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__comment
msgid "Comment"
msgstr "Comentario"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Commenting is not enabled on this course."
msgstr "No se permite comentar en este curso."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Comments"
msgstr "Comentarios"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering questions. In this course, you'll study those topics with "
"activities about mathematics, science and logic."
msgstr ""
"Las tareas comunes de un informático son hacer las preguntas adecuadas y "
"responder preguntas. En este curso, estudiará estos temas con actividades "
"sobre matemáticas, ciencia y lógica."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering questions..."
msgstr ""
"Una de las tareas más comunes de un informático es hacer las preguntas "
"adecuadas y responder dudas..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Communication"
msgstr "Comunicación"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_karma
#: model:gamification.goal.definition,name:website_slides.badge_data_karma_goal
msgid "Community hero"
msgstr "Héroe de la comunidad"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_company_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_company_count
msgid "Company Course Count"
msgstr "Número de cursos de la compañía"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_0
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_0
msgid "Comparing Hardness of Wood Species"
msgstr "Comparación de la dureza de las especies de madera"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_course_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_course
msgid "Complete a course"
msgstr "Completar un curso"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_profile_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_profile
msgid "Complete your profile"
msgstr "Complete su perfil"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
msgid "Completed"
msgstr "Completado"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Completed Course"
msgstr "Curso completado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_completed_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_completed_ids
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Completed Courses"
msgstr "Cursos completados"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completion
msgid "Completion"
msgstr "Finalización"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed_template_id
msgid "Completion Notification"
msgstr "Notificación de finalización"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Completion Time"
msgstr "Tiempo de finalización"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Compose Email"
msgstr "Redactar correo electrónico"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_configuration
msgid "Configuration"
msgstr "Configuración"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_channel_completed
msgid "Congratulations! You completed {{ object.channel_id.name }}"
msgstr "¡Felicidades! Completó {{ object.channel_id.name }}"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations! Your first lesson is available. Let's see the options "
"available here. The tag \"<b>New</b>\" indicates that this lesson was "
"created less than 7 days ago."
msgstr ""
"¡Felicidades! Su primera lección está disponible. Veamos las opciones "
"disponibles. La etiqueta \"<b>Nuevo</b>\" indica que esta lección se creó "
"hace menos de 7 días."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Congratulations, you have reached the last rank!"
msgstr "¡Felicidades! Ha alcanzado el último rango."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations, you've created your first course.<br/>Click on the title of"
" this content to see it in fullscreen mode."
msgstr ""
"¡Felicidades! Ha creado su primer curso.<br/>Haga clic en el título de este "
"contenido para verlo en pantalla completa."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Congratulations, your course has been created, but there isn't any content "
"yet. First, let's add a <b>Section</b> to give your course a structure."
msgstr ""
"¡Felicidades! Su curso ha sido creado, pero aún no tiene contenido. Primero "
"añada una <b>sección</b> para darle estructura a su curso."

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_6_furn3
#, python-format
msgid "Contact Responsible"
msgstr "Contactar al responsable"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Contact the responsible to enroll."
msgstr "Contacte al responsable para inscribirse."

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Contact us"
msgstr "Contáctenos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_content_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_question__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__slide_id
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Content"
msgstr "Contenido"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Content Preview"
msgstr "Vista previa del contenido"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_question
msgid "Content Quiz Question"
msgstr "Pregunta del cuestionario sobre el contenido"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slide_tag
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_content_tags
msgid "Content Tags"
msgstr "Etiquetas del contenido"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Content Title"
msgstr "Título del contenido"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Content Type"
msgstr "Tipo de contenido"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action
msgid ""
"Content are the lessons that compose a course\n"
"                    <br>and can be of different types (presentations, documents, videos, ...)."
msgstr ""
"El contenido son las lecciones que conforman un curso\n"
"                    <br>y pueden ser de diferentes tipos (presentaciones, documentos, vídeos, etc.)."

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_slide_action
#: model:ir.actions.act_window,name:website_slides.slide_slide_action_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__body
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_content
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_contents
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Contents"
msgstr "Contenidos"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Continue"
msgstr "Siguiente"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "Copy Link"
msgstr "Copiar enlace"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_1
msgid "Correct!"
msgstr "¡Correcto!"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_0
msgid "Correct! A shovel is the perfect tool to dig a hole."
msgstr "¡Correcto! Una pala es la herramienta perfecta para cavar un hoyo."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_0
msgid "Correct! A strawberry is a fruit because it's the product of a tree."
msgstr "¡Correcto! Una fresa es una fruta porque es producto de un árbol."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_0
msgid "Correct! Congratulations you have time to loose"
msgstr "¡Correcto! Enhorabuena tiene tiempo libre"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_0
msgid "Correct! You did it!"
msgstr "¡Correcto! ¡Lo logró!"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Could not find your video. Please check if your link is correct and if the "
"video can be accessed."
msgstr ""
"No se encontró su vídeo. Verifique si su enlace es correcto y si se puede "
"acceder al video."

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_form_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_tree_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Course"
msgstr "Curso"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__members
msgid "Course Attendees"
msgstr "Asistentes del curso "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_count
msgid "Course Count"
msgstr "Número de cursos"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Course Finished"
msgstr "Curso finalizado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Group Name"
msgstr "Nombre del grupo del curso"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_group_action
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_course_groups
msgid "Course Groups"
msgstr "Grupos del curso"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_invite_url
msgid "Course Link"
msgstr "Enlace del curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Course Member"
msgstr "Miembro del curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Course Name"
msgstr "Nombre del curso"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slide_channel_pages_list
msgid "Course Pages"
msgstr "Páginas del curso"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/gamification_karma_tracking.py:0
#, python-format
msgid "Course Quiz"
msgstr "Cuestionario del curso "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/mail.py:0
#, python-format
msgid "Course Ranked"
msgstr "Curso clasificado"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Course Set Uncompleted"
msgstr "Curso establecido como no completado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_form
msgid "Course Tag"
msgstr "Etiqueta del curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Tag Group"
msgstr "Grupo de etiquetas del curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Course Tag Groups"
msgstr "Grupos de etiquetas del curso"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_action
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_tree
msgid "Course Tags"
msgstr "Etiquetas del curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Course Title"
msgstr "Título del curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
msgid "Course Type"
msgstr "Tipo de curso"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_finish
msgid "Course finished"
msgstr "Curso finalizado"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Course not published yet"
msgstr "Curso aún no publicado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_rank
msgid "Course ranked"
msgstr "Curso clasificado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_type
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_type
msgid "Course type"
msgstr "Tipo de curso"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Course: %s"
msgstr "Curso: %s"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/res_partner.py:0
#: code:addons/website_slides/models/website.py:0
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_report
#: model:ir.ui.menu,name:website_slides.menu_slide_channel_pages
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_courses
#: model:website.menu,name:website_slides.website_menu_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_graph
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
#, python-format
msgid "Courses"
msgstr "Cursos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
msgid "Courses Page"
msgstr "Página de cursos"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__prerequisite_of_channel_ids
msgid "Courses that have this course as prerequisite."
msgstr "Cursos que tienen este curso como prerequisito."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__cover_properties
msgid "Cover Properties"
msgstr "Propiedades de la portada"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.action_slide_tag
msgid "Create a Content Tag"
msgstr "Crear una etiqueta de contenido"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_tag_group_action
msgid "Create a Course Group"
msgstr "Crear un grupo de curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Create a community and let Attendees answer each others' questions."
msgstr ""
"Cree una comunidad y permita que sus asistentes respondan las preguntas de "
"los demás."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Create new %s '%s'"
msgstr "Crear un nuevo %s '%s'"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "Create new content for your eLearning"
msgstr "Crear nuevo contenido para su eLearning"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_date
msgid "Created on"
msgstr "Creado el"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
msgid "Creation Date"
msgstr "Fecha de creación"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__html_content
msgid "Custom HTML content for slides of category 'Article'."
msgstr ""
"Contenido HTML personalizado para diapositivas de la categoría \"Artículo\"."

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_6_furn3
msgid "DIY Furniture"
msgstr "Muebles de bricolaje"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Fecha (de más recientes a más antiguos)"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Fecha (de más antiguos a más recientes)"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "Defina la visibilidad del desafío a través de los menús"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_enroll
msgid "Defines how people can enroll to your Course."
msgstr "Define cómo las personas se pueden inscribir a su curso."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__promote_strategy
msgid "Defines the content that will be promoted on the course home page"
msgstr ""
"Define el contenido que se promocionará en la página de inicio del curso"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__publish_template_id
msgid ""
"Defines the email your Attendees will receive each time you upload new "
"content."
msgstr ""
"Define el correo electrónico que sus asistentes recibirán cada vez que usted"
" suba nuevo contenido."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__completed_template_id
msgid ""
"Defines the email your Attendees will receive once they reach the end of "
"your course."
msgstr ""
"Define el correo electrónico que sus asistentes recibirán cuando lleguen al "
"final del curso."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__channel_visibility
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_visibility
msgid "Defines who can access your courses and their content."
msgstr "Define quién puede acceder a sus cursos y su contenido."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#, python-format
msgid "Delete"
msgstr "Eliminar"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
#, python-format
msgid "Delete Category"
msgstr "Eliminar categoría"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "Delete Question"
msgstr "Eliminar pregunta"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__description
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Description"
msgstr "Descripción"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_html
msgid "Detailed Description"
msgstr "Descripción detallada"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_1
msgid "Did you read the whole article?"
msgstr "¿Leyó el artículo completo?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Discover more"
msgstr "Descubra más"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Dislike"
msgstr "No me gusta"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__dislikes
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Dislikes"
msgstr "No me gusta"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Display"
msgstr "Mostrar en pantalla"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_question__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_0
msgid "Do you make beams out of lemon trees?"
msgstr "¿Hace vigas de limoneros?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_1
msgid "Do you make lemons out of beams?"
msgstr "¿Hace limones con vigas?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
#, python-format
msgid "Do you really want to leave the course?"
msgstr "¿En verdad desea abandonar el curso?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_0
msgid "Do you think Harry Potted has a good name?"
msgstr "¿Cree que Harry Potted tiene un buen nombre?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Do you want to install the \"%s\" app?"
msgstr "¿Desea instalar la aplicación %s?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_5_3_question_0
msgid "Do you want to reply correctly?"
msgstr "¿Desea responder correctamente?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Do you want to request access to this course?"
msgstr "¿Desea solicitar acceso a este curso?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__document
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "Document"
msgstr "Documento"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__doc
msgid "Document (Word, Google Doc, ...)"
msgstr "Documento (Word, Google Doc, ...)"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_google_url
#, python-format
msgid "Document Link"
msgstr "Enlace del documento"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Document Source"
msgstr "Origen del documento"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__documentation
msgid "Documentation"
msgstr "Documentación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_document
#: model:slide.slide,name:website_slides.slide_category_demo_4_0
msgid "Documents"
msgstr "Documentos"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_2
msgid "Dog Friendly"
msgstr "Se admiten perros"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Don't have an account?"
msgstr "¿No tiene una cuenta?"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Done"
msgstr "Hecho"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__done_count
msgid "Done Count"
msgstr "Número de hechos"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Done!"
msgstr "¡Hecho!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download"
msgstr "Descargar"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download Content"
msgstr "Descargar contenido"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__download_url
msgid "Download URL"
msgstr "Descargar URL"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_10
msgid "Drawing 1"
msgstr "Dibujo 1"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_11
msgid "Drawing 2"
msgstr "Dibujo 2"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_time
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__completion_time
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Duration"
msgstr "Duración"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Earn more Karma to leave a comment."
msgstr "Obtenga más karma para comentar."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Edit"
msgstr "Editar"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Edit in backend"
msgstr "Editar en el backend"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_slide_channel_enroll
msgid "Elearning: Add Attendees to Course"
msgstr "eLearning: añadir asistentes al curso"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_channel_completed
msgid "Elearning: Completed Course"
msgstr "eLearning: curso completado"

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_shared
msgid "Elearning: Course Share"
msgstr "eLearning: compartir el curso"

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_published
msgid "Elearning: New Course Content Notification"
msgstr "eLearning: notificación de nuevo contenido en el curso"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_slide_channel_invite
msgid "Elearning: Promotional Course Invitation"
msgstr "eLearning: invitación a curso promocional"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_email
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Email"
msgstr "Correo electrónico"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_channel_template_id
msgid "Email template used when sharing a channel"
msgstr "Plantilla de correo electrónico usada para compartir un canal"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_slide_template_id
msgid "Email template used when sharing a slide"
msgstr "Plantilla de correo electrónico usada para compartir una diapositiva"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "Email(s) sent."
msgstr "Correo(s) electrónico(s) enviado(s)."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code
msgid "Embed Code"
msgstr "Incrustar código"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_embed_action
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_tree
msgid "Embed Views"
msgstr "Incrustar vistas"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Embed code"
msgstr "Incrustar código"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Embed in another Website"
msgstr "Incrustar en otro sitio web"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_embed
msgid "Embedded Slides View Counter"
msgstr "Contador de vistas de diapositiva incrustadas"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "End course"
msgstr "Finalizar curso"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_2
msgid "Energy Efficiency Facts"
msgstr "Datos sobre la eficiencia de energía"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Enjoy this exclusive content!"
msgstr "¡Disfrute de este contenido exclusivo!"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Enroll Attendees to %(course_name)s"
msgstr "Inscribir asistentes a %(course_name)s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_msg
msgid "Enroll Message"
msgstr "Mensaje de inscripción"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_enroll
msgid "Enroll Policy"
msgstr "Política de inscripción"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__enroll_mode
msgid "Enroll partners"
msgstr "Inscribir contactos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_partner_ids
msgid "Enrolled Attendees Information"
msgstr "Información de los asistentes inscritos"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__partner_ids
msgid "Enrolled partners in the course"
msgstr "Contactos inscritos en el curso"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Enter at least two possible <b>Answers</b>."
msgstr "Introduzca por lo menos dos posibles <b>respuestas</b>."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Enter your <b>Question</b>. Be clear and concise."
msgstr "Introduzca su <b>pregunta</b>. Sea claro y conciso."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__answers_validation_error
msgid "Error on Answers"
msgstr "Error en las respuestas"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Estimated Completion Time"
msgstr "Tiempo estimado para completar"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Evaluate the knowledge of your Attendees and certify their skills."
msgstr ""
"Evalúe el conocimiento de sus asistentes y certifique sus habilidades."

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__public
msgid "Everyone"
msgstr "Todos"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_exercises
msgid "Exercises"
msgstr "Ejercicios"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Exit Fullscreen"
msgstr "Salir de pantalla completa"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code_external
msgid "External Embed Code"
msgstr "Código de incrustación externo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_ids
msgid "External Slide Embeds"
msgstr "Incrustación de diapositivas externas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__url
msgid "External URL"
msgstr "URL externa"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_tree
msgid "External Website"
msgstr "Sitio web externo"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Failed to install \"%s\"."
msgstr "Error al instalar \"%s\"."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promote_strategy
msgid "Featured Content"
msgstr "Contenido destacado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__binary_content
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide_resource__resource_type__file
msgid "File"
msgstr "Archivo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__file_name
msgid "File Name"
msgstr "Nombre del archivo"

#. module: website_slides
#. odoo-javascript
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "File is too big. File size cannot exceed 25MB"
msgstr ""
"El archivo es demasiado grande. El archivo no puede tener un tamaño superior"
" a 25 MB"

#. module: website_slides
#: model:ir.model,name:website_slides.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr "Modelo de ayuda para la transmisión de archivos para controladores"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Filter &amp; order"
msgstr "Filtrar y ordenar"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Finally you can click here to enjoy your content in fullscreen"
msgstr ""
"Por último, puede hacer clic aquí para disfrutar su contenido en pantalla "
"completa"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Finish"
msgstr "Finalizar"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Finish Course"
msgstr "Finalizar curso"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__completed
msgid "Finished"
msgstr "Finalizado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "First Try"
msgstr "Primer intento"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"First, create your lesson, then edit it with the website builder. You'll be "
"able to drop building blocks on your page and edit them."
msgstr ""
"Primero cree su lección, luego edítela con el creador de sitios web. Podrá "
"arrastrar y soltar bloques en su página y editarlos."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "First, let's add a <b>Document</b>. It has to be a .pdf file."
msgstr "Primero, añadamos un <b>documento</b>. Debe ser un archivo PDF."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "First, upload the file on your Google Drive account."
msgstr "Primero, suba el archivo a su cuenta de Google Drive."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "First, upload your videos on Vimeo and mark them as"
msgstr "Primero suba sus vídeos a Vimeo y márquelos como "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "First, upload your videos on YouTube and mark them as"
msgstr "Primero suba sus vídeos a YouTube y márquelos como "

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_follower_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_partner_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome p. ej. fa-tasks"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_0
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Foreword"
msgstr "Prólogo"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_0
msgid "Foreword for this documentation: how to use it, main attention points"
msgstr "Prólogo de esta documentación: cómo usarlo, puntos principales"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_forum
msgid "Forum"
msgstr "Foro"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Fourth Try & More"
msgstr "Cuarto intento o superior"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_2
msgid "From a piece of wood to a fully functional furniture, step by step."
msgstr ""
"Desde una pieza de madera a un mueble totalmente funcional, paso a paso."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action_report
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_partner_action_from_slide
#, python-format
msgid ""
"From here you'll be able to monitor attendees and to track their progress."
msgstr ""
"Desde aquí podrá supervisar a los asistentes y llevar el seguimiento de su "
"progreso."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Fullscreen"
msgstr "Pantalla completa"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_furniture
msgid "Furniture Designer"
msgstr "Diseñador de muebles"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_4_furn1
msgid "Furniture Technical Specifications"
msgstr "Especificaciones técnicas del mueble"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_12
msgid "GLork"
msgstr "GLork"

#. module: website_slides
#: model:ir.model,name:website_slides.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "Desafío de ludificación"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_gardener
msgid "Gardener"
msgstr "Jardinero"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_0
msgid "Gardening: The Know-How"
msgstr "Jardinería: conocimientos fundamentales"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_certification_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_certification
msgid "Get a certification"
msgstr "Obtenga una certificación"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_register
#: model:gamification.goal.definition,name:website_slides.badge_data_register_goal
msgid "Get started"
msgstr "Empezar"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Give your course a helpful <b>Description</b>."
msgstr "Dele una <b>descripción</b> útil a su curso."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Give your course an engaging <b>Title</b>."
msgstr "Dele un <b>título</b> atractivo a su curso."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Go through all its content to see a Course in this section. <br/>"
msgstr "Vea todo el contenido para ver un curso en esta sección. <br/>"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__website_slide_google_app_key
#: model:ir.model.fields,field_description:website_slides.field_website__website_slide_google_app_key
msgid "Google Doc Key"
msgstr "Llave de Google Doc"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__video_source_type__google_drive
#, python-format
msgid "Google Drive"
msgstr "Google Drive"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Google Drive API Key"
msgstr "Clave API de Google Drive"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__google_drive_id
msgid "Google Drive ID of the external URL"
msgstr "ID de Google Drive de la URL externa"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__google_drive_video
msgid "Google Drive Video"
msgstr "Vídeo de Google Drive"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/activity/activity_patch.xml:0
#, python-format
msgid "Grant Access"
msgstr "Otorgar acceso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_graph
msgid "Graph of Contents"
msgstr "Índice"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_id
msgid "Group"
msgstr "Grupo"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Group By"
msgstr "Agrupar por"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__name
msgid "Group Name"
msgstr "Nombre del grupo"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__upload_group_ids
msgid "Group of users allowed to publish contents on a documentation course."
msgstr ""
"Grupo de usuarios con permiso para publicar contenido en un curso de "
"documentación."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_sequence
msgid "Group sequence"
msgstr "Secuencia del grupo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__html_content
msgid "HTML Content"
msgstr "Contenido HTML"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_1
msgid "Hand on!"
msgstr "¡Manos a la obra!"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__prerequisite_user_has_completed
msgid "Has Completed Prerequisite"
msgstr "Tiene prerequisito completado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_search
msgid "Has Menu Entry"
msgstr "Tiene entrada de menú"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_message
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_6
msgid "Here is How to get the Sweetest Strawberries you ever tasted!"
msgstr ""
"¡Aquí aprenderá cómo obtener las fresas más dulces que jamás haya probado!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Home"
msgstr "Inicio"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_1
msgid "Home Gardening"
msgstr "Jardinería doméstica"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_2
msgid "How To Build a HIGH QUALITY Dining Table with LIMITED TOOLS"
msgstr ""
"Cómo construir una mesa de comedor de ALTA CALIDAD con HERRAMIENTAS "
"LIMITADAS"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How do I add new content?"
msgstr "¿Cómo puedo añadir nuevo contenido?"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_3
msgid "How to Grow and Harvest The Best Strawberries | Basics"
msgstr "Cómo cultivar y cosechar las mejores fresas | Fundamentos"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_6
msgid ""
"How to Grow and Harvest The Best Strawberries | Gardening Tips and Tricks"
msgstr ""
"Cómo cultivar y cosechar las mejores fresas | Consejos y trucos de "
"jardinería"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to create a Lesson as an Article?"
msgstr "¿Cómo crear una lección como un artículo?"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_1
msgid "How to find quality wood"
msgstr "Cómo encontrar madera de calidad"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_3
msgid "How to plant a potted tree"
msgstr "Cómo plantar un árbol en maceta"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to upload your PowerPoint Presentations or Word Documents?"
msgstr ""
"¿Cómo subir sus presentaciones de PowerPoint o sus documentos de Word?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to upload your videos?"
msgstr "¿Cómo subir sus vídeos?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "How to use Google Drive?"
msgstr "¿Cómo usar Google Drive?"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_2
msgid "How to wall decorating by tree planting in hanging plastic bottles."
msgstr "Cómo decorar paredes con plantas en botellas de plástico colgantes."

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_howto
msgid "HowTo"
msgstr "Cómo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__id
#: model:ir.model.fields,field_description:website_slides.field_slide_question__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__id
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__id
msgid "ID"
msgstr "ID"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcada, hay nuevos mensajes que requieren su atención."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen error de envío."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_4_furn1
msgid ""
"If you are looking for technical specifications, have a look at this "
"documentation."
msgstr ""
"Si está buscando especificaciones técnicas, eche un vistazo a esta "
"documentación."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"If you want to be sure that attendees have understood and memorized the "
"content, you can add a Quiz on the lesson. Click on <b>Add Quiz</b>."
msgstr ""
"Si desea asegurarse de que los asistentes entendieron y memorizaron el "
"contenido, puede añadir un cuestionario en esta lección. Haga clic en "
"<b>añadir cuestionario</b>."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"If you want to use other types of files, you may want to use an external "
"source (Google Drive) instead."
msgstr ""
"Si desea usar otro tipo de archivos, tal vez quiera usar una fuente externa "
"(Google Drive) en su lugar."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1920
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1920
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__infographic
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__image
#, python-format
msgid "Image"
msgstr "Imagen"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1024
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1024
msgid "Image 1024"
msgstr "Imagen 1024"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_128
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_128
msgid "Image 128"
msgstr "Imagen 128"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_256
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_256
msgid "Image 256"
msgstr "Imagen 256"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_512
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_512
msgid "Image 512"
msgstr "Imagen 512"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_binary_content
msgid "Image Content"
msgstr "Contenido de la imagen"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_google_url
#, python-format
msgid "Image Link"
msgstr "Enlace de la imagen"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Image Source"
msgstr "Origen de la imagen"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid ""
"Impossible to send emails. Select a \"Channel Share Template\" for courses "
"%(course_names)s first"
msgstr ""
"No es posible enviar correos electrónicos. Primero seleccione una "
"\"plantilla para compartir el canal\" para los cursos %(course_names)s. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Impossible to send emails. Select a \"Share Template\" for courses "
"%(course_names)s first"
msgstr ""
"No es posible enviar correos electrónicos. Primero seleccione una "
"\"plantilla para compartir\" para los cursos %(course_names)s."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_0
msgid "Incorrect!"
msgstr "¡Incorrecto!"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_1
msgid "Incorrect! A strawberry is not a vegetable."
msgstr "¡Incorrecto! Una fresa no es una verdura."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_2
msgid "Incorrect! A table is a piece of furniture."
msgstr "¡Incorrecto! Una mesa es un mueble."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_1
msgid "Incorrect! Good luck digging a hole with a spoon..."
msgstr "¡Incorrecto! Buena suerte cavando un agujero con una cuchara..."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_2
msgid "Incorrect! Seriously?"
msgstr "¡Incorrecto! ¿En serio?"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_1
msgid "Incorrect! You better think twice..."
msgstr "¡Incorrecto! Mejor piénselo otra vez..."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_1
msgid "Incorrect! You really should read it."
msgstr "¡Incorrecto! En verdad debería leerlo."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_2
msgid "Incorrect! of course not ..."
msgstr "¡Incorrecto! Claro que no..."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_infographic
msgid "Infographics"
msgstr "Infografías"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Install"
msgstr "Instalar"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Install the"
msgstr "Instale la aplicación"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Installing \"%s\"."
msgstr "Instalando \"%s\"."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_0
msgid "Interesting Facts"
msgstr "Datos interesantes"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_1
msgid "Interesting Tree Facts"
msgstr "Datos interesantes sobre árboles"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_1
msgid "Interesting information about home gardening. Keep it close!"
msgstr ""
"Es información interesante sobre la jardinería casera. ¡Téngala a la mano!"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_intermediate
msgid "Intermediate"
msgstr "Intermedio"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Internal server error, please try again later or contact administrator.\n"
"Here is the error message: %s"
msgstr ""
"Error interno del servidor, inténtelo de nuevo más tarde o póngase en contacto con el administrador.\n"
"Este es el mensaje de error: %s"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Invalid file type. Please select pdf or image file"
msgstr "Tipo de archivo no válido, seleccione un PDF o archivo de imagen"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__invitation_link
msgid "Invitation Link"
msgstr "Enlace de invitación"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Invite"
msgstr "Invitar"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Invite Attendees to %(course_name)s"
msgstr "Invitar asistentes a %(course_name)s"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__invited
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Invite Sent"
msgstr "Invitación enviada"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_has_completed_category
msgid "Is Category Completed"
msgstr "Es una categoría completida"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "Es un editor"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_member
msgid "Is Enrolled Attendee"
msgstr "Es un asistente inscrito"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_is_follower
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_member_invited
msgid "Is Invited Attendee"
msgstr "Es un asistente invitado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_has_completed
msgid "Is Member"
msgstr "Es miembro"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_new_slide
msgid "Is New Slide"
msgstr "Es la nueva diapositiva"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_published
msgid "Is Published"
msgstr "Está publicado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_category
msgid "Is a category"
msgstr "Es una categoría"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__is_correct
msgid "Is correct answer"
msgstr "Es respuesta correcta"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__is_member
msgid "Is the attendee actively enrolled."
msgstr "El asistente está inscrito activamente."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__is_member_invited
msgid "Is the invitation for this attendee pending."
msgstr "¿La invitación para este asistente está pendiente?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "It should look similar to"
msgstr "Debería parecerse a"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_3
msgid ""
"Jim and Todd plant a potted tree for a customer of Knecht's Nurseries and "
"Landscaping. Narrated by Leif Knecht, owner."
msgstr ""
"Jim y Todd plantan un árbol en maceta para un cliente de Viveros y "
"Paisajismo de Knecht. Narrado por Leif Knecht, propietario."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "Join & Submit"
msgstr "Unirse y enviar"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "Join the Course"
msgstr "Unirse al curso"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Join the course to take the quiz and verify your answers!"
msgstr ""
"¡Únase al curso para realizar el cuestionario y verificar sus respuestas!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
#, python-format
msgid "Join this Course"
msgstr "Unirse a este curso"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__joined
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Joined"
msgstr "Unido"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_2
msgid "Just some basics Energy Efficiency Facts."
msgstr "Solo algunos datos básicos sobre Eficiencia en Energía"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_1
msgid "Just some basics Interesting Tree Facts."
msgstr "Solo algunos datos básicos interesantes sobre Árbol"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_0
msgid "Just some basics Tree Infographic."
msgstr "Una infografía básica sobre árboles."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Karma"
msgstr "Karma"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_comment
msgid "Karma needed to add a comment on a slide of this course"
msgstr "Karma necesario para comentar en las diapositivas de este curso"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_review
msgid "Karma needed to add a review on the course"
msgstr "Karma necesario para añadir una reseña para este curso"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_vote
msgid "Karma needed to like/dislike a slide of this course."
msgstr ""
"Karma necesario para dejar Me gusta o No me gusta en las diapositivas de "
"este curso."

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_profile
#: model:gamification.goal.definition,name:website_slides.badge_data_profile_goal
msgid "Know yourself"
msgstr "Conózcase a sí mismo"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_3_furn0
msgid ""
"Knowing which kind of wood to use depending on your application is important. In this course you\n"
"will learn the basics of wood characteristics."
msgstr ""
"Es importante saber qué tipo de madera usar según su aplicación. En este curso\n"
"aprenderá los conceptos básicos de las características de la madera."

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_3
msgid ""
"Knowing wood characteristics is a requirement in order to know which kind of"
" wood to use in a given situation."
msgstr ""
"Conocer las características de la madera es un requisito para saber qué tipo"
" de madera usar en una situación dada."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__lang
msgid "Language"
msgstr "Idioma"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Last Action On"
msgstr "Última acción el"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Last Invitation"
msgstr "Última invitación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__last_invitation_date
msgid "Last Invitation Date"
msgstr "Fecha de la última invitación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_last_update
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Last Update"
msgstr "Última actualización"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__latest
msgid "Latest Created"
msgstr "Creado más recientemente"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_latest_achievements
msgid "Latest achievements"
msgstr "Logros más recientes"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "Leaderboard"
msgstr "Tabla de clasificación"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_1_gard1
msgid ""
"Learn how to take care of your favorite trees. Learn when to plant, how to "
"manage potted trees, ..."
msgstr ""
"Aprenda a cuidar sus árboles favoritos. Aprenda cuándo plantarlos, cómo "
"supervisar árboles en macetas, etc."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_0_gard_0
msgid "Learn the basics of gardening!"
msgstr "¡Aprenda los fundamentos de la jardinería!"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_1
msgid "Learn to identify quality wood in order to create solid furnitures."
msgstr ""
"Aprenda a identificar la calidad de la madera con el fin de crear muebles "
"sólidos."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.js:0
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
#, python-format
msgid "Leave the course"
msgstr "Abandonar el curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Lesson"
msgstr "Lección"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Lesson Nav"
msgstr "Navegación de lección"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Lessons"
msgstr "Lecciones"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz_finish.js:0
#, python-format
msgid "Level up!"
msgstr "¡Suba de nivel!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Like"
msgstr "Me gusta"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__likes
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Likes"
msgstr "Gustos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__link
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide_resource__resource_type__url
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Link"
msgstr "Enlace"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__document_google_url
msgid ""
"Link of the document (we currently only support Google Drive as source)"
msgstr ""
"Enlace del documento (actualmente sólo admitimos Google Drive como fuente)"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__image_google_url
msgid "Link of the image (we currently only support Google Drive as source)"
msgstr ""
"Enlace de la imagen (actualmente sólo admitimos Google Drive como fuente)"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__video_url
msgid ""
"Link of the video (we support YouTube, Google Drive and Vimeo as sources)"
msgstr "Enlace del vídeo (aceptamos vídeos de YouTube, Google Drive y Vimeo)"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Loading content..."
msgstr "Cargando contenido..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "Log in"
msgstr "Iniciar sesión"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__template_id
msgid "Mail Template"
msgstr "Plantilla de correo electrónico"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_mass_mailing_slides
msgid "Mailing"
msgstr "Envío de correo"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_0
msgid "Main Trees Categories"
msgstr "Categorías de Árboles Principales"

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_manager
msgid "Manager"
msgstr "Gerente"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Mark Done"
msgstr "Marcar como hecho"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#, python-format
msgid "Mark To Do"
msgstr "Marcar como pendiente"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
#, python-format
msgid "Mark as done"
msgstr "Marcar como hecho"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
#, python-format
msgid "Mark as not done"
msgstr "Marcar como no hecho"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Mark the correct answer by checking the <b>correct</b> mark."
msgstr ""
"Marque la respuesta correcta al seleccionar la casilla <b>correcta</b>."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Members"
msgstr "Miembros"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Members Views"
msgstr "Vistas de miembros"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_group_ids
msgid ""
"Members of those groups are automatically added as members of the channel."
msgstr ""
"Miembros de esos grupos son añadidos automáticamente como miembros del "
"canal."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Menu Entry"
msgstr "Entrada de menú"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_msg
msgid "Message explaining the enroll process"
msgstr "Mensaje que explica el proceso de inscripción"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_1
msgid "Methods"
msgstr "Métodos"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_2
msgid "Mighty Carrots"
msgstr "Poderosas zanahorias"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_1
msgid ""
"Mighty forest just don't appear in a few weeks. Learn how time made our "
"forests mighty and mysterious."
msgstr ""
"Un inmenso bosque no aparece en solo un par de semanas. Aprenda cómo el "
"tiempo hizo que nuestros bosques fueran inmensos y misteriosos."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Minutes"
msgstr "Minutos"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Missing \"Tag Group\" for creating a new \"Tag\"."
msgstr "Falta el \"grupo de etiqueta\" para crear una nueva \"etiqueta\"."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Mobile sub-nav"
msgstr "Subnavegación móvil"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "More info"
msgstr "Más información"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_viewed
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Most Viewed"
msgstr "Más visto"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_voted
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Most Voted"
msgstr "Más votado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Most popular courses"
msgstr "Cursos más populares"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "My Content"
msgstr "Mi contenido"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "My Courses"
msgstr "Mis cursos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "My courses"
msgstr "Mis cursos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__name
msgid "Name"
msgstr "Nombre"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Nav"
msgstr "Navegación"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Need help? Review related content:"
msgstr "¿Necesite ayuda? Revise el contenido relacionado:"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/views/slide_channel_partner_list/slide_channel_partner_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#, python-format
msgid "New"
msgstr "Nuevo"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "New Certification"
msgstr "Nueva certificación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__publish_template_id
msgid "New Content Notification"
msgstr "Nueva notificación de contenido"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
msgid "New Content Ribbon"
msgstr "Nueva cinta de contenido"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_add
msgid "New Course"
msgstr "Nuevo curso"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_published
msgid ""
"New {{ object.slide_category }} published on {{ object.channel_id.name }}"
msgstr ""
"Nueva {{ object.slide_category }} publicada en {{ object.channel_id.name }}"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Newest"
msgstr "Más reciente"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Newest courses"
msgstr "Cursos más recientes"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#, python-format
msgid "Next"
msgstr "Siguiente"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__next_slide_id
msgid "Next Lesson"
msgstr "Siguiente lección"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Next rank:"
msgstr "Siguiente rango:"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_1
#, python-format
msgid "No"
msgstr "No"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "No Attendee has completed this course yet!"
msgstr "¡Ningún asistente ha completado este curso!"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "No Attendees Yet!"
msgstr "Todavía no hay asistentes"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "No Course created yet."
msgstr "Todavía no se ha creado un curso."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "No Notification"
msgstr "Sin notificación"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid "No Quiz data yet!"
msgstr "Todavía no hay datos del cuestionario"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel
msgid "No Reviews yet!"
msgstr "Todavía no hay reseñas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No completed courses yet!"
msgstr "Todavía no hay cursos completados"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "No content was found using your search"
msgstr "No se ha encontrado contenido usando su búsqueda"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search"
msgstr "No se ha encontrado un curso que coincida con su búsqueda"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search."
msgstr "No se ha encontrado un curso que coincida con su búsqueda."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "No data yet!"
msgstr "¡Todavía no hay información!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "No leaderboard currently :("
msgstr "No hay tabla de clasificación :("

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_placeholder
msgid "No lessons are available yet."
msgstr "Todavía no hay lecciones disponibles."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No ongoing courses yet!"
msgstr "Todavía no hay cursos en progreso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "No presentation available."
msgstr "No hay presentaciones disponibles."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No results found for '"
msgstr "No se han encontrado resultados para \""

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__none
msgid "None"
msgstr "Ninguno"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_pages_kanban_view
msgid "Not Published"
msgstr "No publicado"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Not enough karma to comment"
msgstr "Sin suficiente karma para comentar"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Not enough karma to review"
msgstr "Sin suficiente karma para dejar reseñas"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.js:0
#, python-format
msgid "Notifications"
msgstr "Notificaciones"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_article
msgid "Number of Articles"
msgstr "Número de artículos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_slides
msgid "Number of Contents"
msgstr "Número de contenidos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_document
msgid "Number of Documents"
msgstr "Número de documentos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_infographic
msgid "Number of Images"
msgstr "Número de imágenes"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_quiz
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_quiz
msgid "Number of Quizs"
msgstr "Número de cuestionarios"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_video
msgid "Number of Videos"
msgstr "Número de vídeos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__comments_count
msgid "Number of comments"
msgstr "Número de comentarios"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__questions_count
msgid "Numbers of Questions"
msgstr "Números de preguntas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Odoo"
msgstr "Odoo"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Odoo • Image and Text"
msgstr "Odoo • Imagen y texto"

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_officer
msgid "Officer"
msgstr "Encargado"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "On Google Drive"
msgstr "En Google Drive"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__invite
msgid "On Invitation"
msgstr "Por invitación"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "On Vimeo"
msgstr "En Vimeo"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "On YouTube"
msgstr "En YouTube"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Once you're done, don't forget to <b>Publish</b> your course."
msgstr "En cuanto acabe, no olvide <b>publicar</b> su curso."

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__ongoing
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Ongoing"
msgstr "En progreso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Ongoing Courses"
msgstr "Cursos en progreso"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/mail.py:0
#, python-format
msgid "Only a single review can be posted per course."
msgstr "Solo se puede publicar una reseña por curso."

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__public
msgid "Open"
msgstr "Abierto"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operación no admitida"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Idioma de traducción opcional (código ISO) a seleccionar para el envío de "
"correos electrónicos. Si no se selecciona esta opción, se utilizará la "
"versión en inglés. Por lo general, se usa una expresión de marcador de "
"posición para indicar el idioma adecuado, por ejemplo, {{ "
"object.partner_id.lang }}."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Options"
msgstr "Opciones"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_binary_content
msgid "PDF Content"
msgstr "Contenido PDF"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Paid Courses"
msgstr "Cursos pagados"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_has_new_content
msgid "Partner Has New Content"
msgstr "El contacto tiene contenido nuevo"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_pivot
msgid "Pivot"
msgstr "Tabla dinámica"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "Please"
msgstr "Por favor"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid ""
"Please <a href=\"/web/login?redirect=%s\">login</a> or <a "
"href=\"/web/signup?redirect=%s\">create an account</a> to vote for this "
"lesson"
msgstr ""
"<a href=\"/web/login?redirect=%s\">Inicie sesión</a> o <a "
"href=\"/web/signup?redirect=%s\">cree una cuenta</a> para votar en esta "
"lección"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid ""
"Please <a href=\"/web/login?redirect=%s\">login</a> to vote for this lesson"
msgstr ""
"<a href=\"/web/login?redirect=%s\">Inicie sesión</a> para votar en esta "
"lección"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Please enter a valid Vimeo video link"
msgstr "Introduzca un enlace de vídeo de Vimeo válido"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Please enter valid Google Drive Link"
msgstr "Introduzca un enlace de Google Drive válido"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_fullscreen_player.js:0
#: code:addons/website_slides/static/src/js/slides_course_fullscreen_player.js:0
#: code:addons/website_slides/static/src/js/slides_share.js:0
#: code:addons/website_slides/static/src/js/slides_share.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "Please enter valid email(s)"
msgstr "Introduzca correo(s) electrónico(s) válido(s)"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz_question_form.js:0
#, python-format
msgid "Please fill in the question"
msgstr "Complete la pregunta"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid "Please select at least one recipient."
msgstr "Seleccione al menos un destinatario."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Points Rewards"
msgstr "Puntos de recompensa"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_course
#: model:gamification.goal.definition,name:website_slides.badge_data_course_goal
msgid "Power User"
msgstr "Usuario experto"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Powered by"
msgstr "Con tecnología de"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__prerequisite_of_channel_ids
msgid "Prerequisite Of"
msgstr "Prerrequisito de "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__prerequisite_channel_ids
msgid "Prerequisite courses to complete before accessing this one."
msgstr "Cursos de prerrequisito a completar antes de acceder a este."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__prerequisite_channel_ids
msgid "Prerequisites"
msgstr "Prerrequisitos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__slide_id
#: model:slide.slide,name:website_slides.slide_slide_demo_4_12
msgid "Presentation"
msgstr "Presentación"

#. module: website_slides
#: model:mail.message.subtype,description:website_slides.mt_channel_slide_published
#: model:mail.message.subtype,name:website_slides.mt_channel_slide_published
msgid "Presentation Published"
msgstr "Presentación publicada"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Preview"
msgstr "Vista previa"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Previous"
msgstr "Anterior"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Private Course"
msgstr "Curso privado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Progress"
msgstr "Progreso"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
#, python-format
msgid "Progress bar"
msgstr "Barra de progreso"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promoted_slide_id
msgid "Promoted Slide"
msgstr "Diapositiva promovida"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Public Views"
msgstr "Vistas públicas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "Fecha de publicación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__date_published
msgid "Publish Date"
msgstr "Fecha de publicación"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Published"
msgstr "Publicado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Published Contents"
msgstr "Contenidos publicados"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Published Date"
msgstr "Fecha publicada"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Publishing is restricted to the responsible of training courses or members "
"of the publisher group for documentation courses"
msgstr ""
"Publicar está restringido al responsable del curso o miembros del grupo de "
"publicadores para cursos de documentación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__question_id
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Question"
msgstr "Pregunta"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__question
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Question Name"
msgstr "Nombre de la pregunta"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__question_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Questions"
msgstr "Preguntas"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__quiz
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__quiz
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_0
#: model_terms:ir.ui.view,arch_db:website_slides.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "Quiz"
msgstr "Cuestionario"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Quiz Completed"
msgstr "Cuestionario completado"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Quiz Demo Data"
msgstr "Datos de demostración del cuestionario"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "Quiz Set Uncompleted"
msgstr "Cuestionario marcado como no completado"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__quiz_attempts_count
msgid "Quiz attempts count"
msgstr "Número de intentos del cuestionario"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_question_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_quizzes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree_report
msgid "Quizzes"
msgstr "Cuestionarios"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg_stars
msgid "Rating Average (Stars)"
msgstr "Calificación promedio (estrellas)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg_text
msgid "Rating Avg Text"
msgstr "Texto de calificación promedio"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Última retroalimentación de la calificación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_image
msgid "Rating Last Image"
msgstr "Última imagen de la calificación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_value
msgid "Rating Last Value"
msgstr "Último valor de la calificación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Satisfacción de calificación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_text
msgid "Rating Text"
msgstr "Texto de calificación"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_count
msgid "Rating count"
msgstr "Número de calificación"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "Rating of %s"
msgstr "Calificación de %s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__rating_ids
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_form_slides
msgid "Ratings"
msgstr "Calificaciones"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_karma_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_karma
msgid "Reach 2000 XP"
msgstr "Alcance 2000 XP"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Reach new heights"
msgstr "Alcance nuevas alturas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__partner_ids
msgid "Recipients"
msgstr "Destinatarios"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/activity/activity_patch.xml:0
#, python-format
msgid "Refuse Access"
msgstr "Rechazar acceso"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_register_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_register
msgid "Register to the platform"
msgstr "Inscríbase en la plataforma"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Related"
msgstr "Relacionado"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Remove the answer comment"
msgstr "Remover el comentario de la respuesta"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Remove this answer"
msgstr "Remover esta respuesta"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__render_model
msgid "Rendering Model"
msgstr "Modelo de visualización"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report
msgid "Reporting"
msgstr "Informes"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Request Access."
msgstr "Solicitar acceso."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Request sent!"
msgstr "¡Solicitud enviada!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Reset"
msgstr "Restablecer"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide_resource.py:0
#: code:addons/website_slides/models/slide_slide_resource.py:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__data
#, python-format
msgid "Resource"
msgstr "Recurso"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide_resource.py:0
#, python-format
msgid ""
"Resource %(resource_name)s is a link and should not contain a data file"
msgstr ""
"El recurso %(resource_name)s es un enlace y no debería contener un archivo "
"de datos"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__resource_type
msgid "Resource Type"
msgstr "Tipo de recurso"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__user_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_user_id
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Responsible"
msgstr "Responsable"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Responsible already contacted."
msgstr "Responsable ya contactado."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_id
msgid "Restrict publishing to this website."
msgstr "Restrinja la publicación en este sitio web."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__source_type__external
#, python-format
msgid "Retrieve from Google Drive"
msgstr "Recuperar de Google Drive"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Retry"
msgstr "Volver a intentar"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.profile_access_denied
msgid "Return to the course."
msgstr "Vuelva al curso."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Review Course"
msgstr "Escribir reseña del curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_tree_slide_channel
msgid "Review Date"
msgstr "Fecha de reseña"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_reviews
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_graph_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Reviews"
msgstr "Reseñas"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/portal_chatter.js:0
#, python-format
msgid "Reviews (%s)"
msgstr "Reseñas (%s)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_fourth_attempt_reward
msgid "Reward: every attempt after the third try"
msgstr "Recompensa: cada intento después del tercero"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_first_attempt_reward
msgid "Reward: first attempt"
msgstr "Recompensa: primer intento"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_second_attempt_reward
msgid "Reward: second attempt"
msgstr "Recompensa: segundo intento"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_third_attempt_reward
msgid "Reward: third attempt"
msgstr "Recompensa: tercer intento"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Rewards"
msgstr "Recompensas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_seo_optimized
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_seo_optimized
msgid "SEO optimized"
msgstr "Optimizado para SEO"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de envío del SMS"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__embed_code_external
msgid ""
"Same as 'Embed Code' but used to embed the content on an external website."
msgstr ""
"Igual al \"código de incrustación\" pero se utiliza para incrustar el "
"contenido en un sitio web externo."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "Sample"
msgstr "Muestra"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Save"
msgstr "Guardar"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "Save & Publish"
msgstr "Guardar y publicar"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Save your presentations or documents as PDF files and upload them."
msgstr "Guarde sus presentaciones o documentos como archivos PDF y súbalos."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_tree_slide_channel
msgid "Score"
msgstr "Puntuación"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search"
msgstr "Buscar"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Search Contents"
msgstr "Buscar contenido"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Search courses"
msgstr "Buscar cursos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search in content"
msgstr "Buscar en el contenido"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Second Try"
msgstr "Segundo intento"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__category_id
#, python-format
msgid "Section"
msgstr "Sección"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Section Subtitle"
msgstr "Subtítulo de la sección"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/category_add_dialog/category_add_dialog.xml:0
#, python-format
msgid "Section name"
msgstr "Nombre de la sección"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__access_token
msgid "Security Token"
msgstr "Token de seguridad"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/res_users.py:0
#, python-format
msgid "See our eLearning"
msgstr "Vea nuestro eLearning"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid "Select <b>Course</b> to create it and manage it."
msgstr "Seleccione <b>Curso</b> para empezar a crearlo y gestionarlo."

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__specific
msgid "Select Manually"
msgstr "Seleccionar manualmente"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Select the correct answer below:"
msgstr "Seleccione la respuesta correcta:"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Sell access to your courses on your website and track revenues."
msgstr ""
"Venda el acceso a sus cursos en su sitio web y lleve el seguimiento de "
"ingresos."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_sale_slides
msgid "Sell on eCommerce"
msgstr "Vender en Comercio electrónico"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Send"
msgstr "Enviar"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__send_email
#, python-format
msgid "Send Email"
msgstr "Enviar correo electrónico"

#. module: website_slides
#: model:mail.template,description:website_slides.mail_template_channel_completed
msgid "Sent to attendees once they've completed the course"
msgstr "Enviado a los asistentes una vez que completen el curso"

#. module: website_slides
#: model:mail.template,description:website_slides.slide_template_published
msgid "Sent to attendees when new course is published"
msgstr "Enviado a los asistentes cuando se publica un nuevo curso"

#. module: website_slides
#: model:mail.template,description:website_slides.mail_template_slide_channel_enroll
msgid "Sent to attendees when they are added to a course"
msgstr "Enviado a los asistentes cuando se les añade a un curso"

#. module: website_slides
#: model:mail.template,description:website_slides.mail_template_slide_channel_invite
msgid "Sent to potential attendees to check out the course."
msgstr "Enviado a asistentes potenciales para que revisen el curso. "

#. module: website_slides
#: model:mail.template,description:website_slides.slide_template_shared
msgid "Sent when attendees share the course by email"
msgstr ""
"Enviado cuando los asistentes comparten el curso por correo electrónico"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__seo_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__seo_name
msgid "Seo name"
msgstr "Nombre SEO"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_question__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.website_slides_action_settings
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_settings
msgid "Settings"
msgstr "Ajustes"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Share"
msgstr "Compartir"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Share Channel"
msgstr "Compartir canal"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
#, python-format
msgid "Share Link"
msgstr "Compartir enlace"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_slide_template_id
msgid "Share Template"
msgstr "Plantilla para compartir"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_fullscreen_player.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
#, python-format
msgid "Share This Content"
msgstr "Compartir este contenido"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Share This Course"
msgstr "Compartir este curso"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_share_url
msgid "Share URL"
msgstr "Compartir URL"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "Share by Email"
msgstr "Compartir por correo electrónico"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on Facebook"
msgstr "Compartir en Facebook"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on LinkedIn"
msgstr "Compartir en Linkedin"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on Pinterest"
msgstr "Compartir en Pinterest"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_body
#, python-format
msgid "Share on Social Media"
msgstr "Compartir en redes sociales"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on Twitter"
msgstr "Compartir en Twitter"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
#, python-format
msgid "Share on Whatsapp"
msgstr "Compartir en WhatsApp"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "Sharing is caring!"
msgstr "¡Compartir es importante!"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__sheet
msgid "Sheet (Excel, Google Sheet, ...)"
msgstr "Hoja (Excel, Google Sheet, etc.)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_short
msgid "Short Description"
msgstr "Descripción corta"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_visibility
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_visibility
msgid "Show Course To"
msgstr "Mostrar curso a "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_4
msgid "Show your newly mastered knowledge!"
msgstr "¡Muestre el conocimiento que acaba de dominar!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Sign Up!"
msgstr "¡Inscríbase!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Sign in and join the course to verify your answers!"
msgstr "¡Inicie sesión y únase al curso para verificar sus respuestas!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "Sign up"
msgstr "Inscribirse"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__connected
msgid "Signed In"
msgstr "Conectado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid ""
"Skill up and have an impact! Your business career starts here.<br/>Time to "
"start a course."
msgstr ""
"¡Aumente sus habilidades y tenga un impacto! Su carrera empresarial empieza "
"aquí. <br/> Es hora de iniciar un curso."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__slide_id
msgid "Slide"
msgstr "Diapositiva"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_partner
msgid "Slide / Partner decorated m2m"
msgstr "Diapositiva/contacto decorado m2m"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_icon_class
msgid "Slide Icon fa-class"
msgstr "Diapositiva Icon fa-class"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_answer
msgid "Slide Question's Answer"
msgstr "La respuesta de la pregunta de la diapositiva"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_tag
msgid "Slide Tag"
msgstr "Etiqueta de diapositiva"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_type
msgid "Slide Type"
msgstr "Tipo de diapositiva"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_partner_ids
msgid "Slide User Data"
msgstr "Datos del usuario de diapositiva"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
#, python-format
msgid "Slide image"
msgstr "Imagen de la diapositiva"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid ""
"Slide with questions must be marked as done when submitting all good answers"
" "
msgstr ""
"Las diapositivas con preguntas se deben marcar como hechas al enviar "
"respuestas correctas"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_fullscreen.xml:0
#: model:ir.model,name:website_slides.model_slide_slide
#, python-format
msgid "Slides"
msgstr "Diapositivas"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__slides
msgid "Slides (PowerPoint, Google Slides, ...)"
msgstr "Diapositivas (PowerPoint, Google Slides, etc.)"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_ids
msgid "Slides and categories"
msgstr "Diapositivas y categorías"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_6_furn3
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_6_furn3
msgid "So much amazing certification."
msgstr "Tanta certificación increíble."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Sort by"
msgstr "Ordenar por"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__source_type
msgid "Source Type"
msgstr "Tipo de origen"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Start Course"
msgstr "Comenzar curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Start this Course"
msgstr "Comenzar este curso"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Start with the customer – find out what they want and give it to them."
msgstr "Comience con el cliente – descubra lo que quiere y déselo."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Start your online course today!"
msgstr "¡Comience hoy mismo su curso en línea!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Status"
msgstr "Estado"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha límite ya ha pasado\n"
"Hoy: la fecha límite es hoy\n"
"Planificada: actividades futuras."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__subject
msgid "Subject"
msgstr "Asunto"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Subject..."
msgstr "Asunto..."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.js:0
#, python-format
msgid "Subscribe"
msgstr "Suscribirse"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information"
msgstr "Información del suscritor"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information for the current logged in user"
msgstr "Información del suscriptor que abrió la sesión actual"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__partner_ids
msgid "Subscribers"
msgstr "Suscritores"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_partner_ids
msgid "Subscribers information"
msgstr "Información de los suscritores"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_type
msgid ""
"Subtype of the slide category, allows more precision on the actual file type"
" / source type."
msgstr ""
"Subtipo de la categoría de diapositivas, permite una mayor precisión en el "
"tipo de archivo/tipo de fuente real."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "Succeed and gain karma"
msgstr "Si tiene éxito, recibirá karma"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#: code:addons/website_slides/static/src/xml/website_slides_channel_tag.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_form
#, python-format
msgid "Tag"
msgstr "Etiqueta"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_channel_tag.xml:0
#, python-format
msgid "Tag Group"
msgstr "Grupo de etiqueta"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_tag_add.js:0
#, python-format
msgid "Tag Group (required for new tags)"
msgstr "Grupo de etiqueta (necesario para etiquetas nuevas)"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Tag Name"
msgstr "Nombre de etiqueta"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag__color
msgid ""
"Tag color used in both backend and website. No color means no display in "
"kanban or front-end, to distinguish internal tags from public categorization"
" tags"
msgstr ""
"Color de etiqueta utilizada en el backend y en el sitio web. Sin color "
"significa que que no aparece en el kanban o el frontend, para distinguir "
"etiquetas internas de las etiquetas de categorización públicas"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__tag_ids
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_data_other
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
#, python-format
msgid "Tags"
msgstr "Etiquetas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Tags..."
msgstr "Etiquetas..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Take Quiz"
msgstr "Responder el cuestionario"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_1_gard1
msgid "Taking care of Trees"
msgstr "Cuidado de los árboles"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_4_1
msgid "Technical Drawings"
msgstr "Dibujos técnicos"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_10
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_11
msgid "Technical drawing"
msgstr "Dibujo técnico"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_2
msgid "Test Yourself"
msgstr "Póngase a prueba"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_4
msgid "Test your knowledge"
msgstr "Pruebe su conocimiento"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_3
msgid "Test your knowledge!"
msgstr "¡Pruebe su conocimiento!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Test your students with small Quizzes"
msgstr "Ponga a prueba a sus estudiantes con pequeños cuestionarios"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"The <b>Duration</b> of the lesson is based on the number of pages of your "
"document. You can change this number if your attendees will need more time "
"to assimilate the content."
msgstr ""
"La <b>duración</b> de la lección se basa en el número de páginas de su "
"documento. Puede cambiar este número si sus asistentes necesitarán más "
"tiempo para asimilar el contenido."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"The <b>Title</b> of your lesson is autocompleted but you can change it if "
"you want.</br>A <b>Preview</b> of your file is available on the right side "
"of the screen."
msgstr ""
"El <b>título</b> de su lección se autocompleta pero puede cambiarlo si lo "
"desea. </br> Una <b>vista previa</b> de su archivo esta disponible en el "
"lado derecho de la pantalla. "

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_channel_check_enroll
msgid ""
"The Enroll Policy should be set to 'On Invitation' when visibility is set to"
" 'Course Attendees'"
msgstr ""
"La política de inscripción debe estar establecida como \"Por invitación\" "
"cuando la visibilidad esta establecida como \"Asistentes del curso\". "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"The Google Drive link can be obtained by using the 'share' button in the "
"Google interface."
msgstr ""
"Puede obtener el enlace de Google Drive al usar el botón de \"Compartir\" en"
" la interfaz de Google."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"The Google Drive link to use here can be obtained by clicking the \"Share\" "
"button in the Google interface."
msgstr ""
"El enlace de Google Drive a utilizar aquí se puede obtener al hacer clic en "
"el botón de \"Compartir\" de la interfaz de Google."

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_channel_partner_check_completion
msgid ""
"The completion of a channel is a percentage and should be between 0% and "
"100."
msgstr ""
"Un canal se muestra como completado a través de porcentajes y deben estar "
"entre 0 % y 100 %."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "The contact associated with this invitation does not seem to be valid."
msgstr "Parece que el contacto asociado con esta invitación no es válido. "

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__is_preview
msgid ""
"The course is accessible by anyone : the users don't need to join the "
"channel to access the content of the course."
msgstr ""
"Todos pueden acceder al curso: los usuarios no necesitan unirse al canal "
"para acceder al contenido."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description_short
msgid "The description that is displayed on the course card"
msgstr "La descripción que aparece en la tarjeta del curso"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description
msgid ""
"The description that is displayed on top of the course page, just below the "
"title"
msgstr ""
"La descripción que aparece en la parte superior de la página del curso, "
"debajo del título"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_url
msgid "The full URL to access the document through the website."
msgstr "La URL completa para acceder al documento a través del sitio web."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__can_self_mark_completed
msgid "The slide can be marked as completed even without opening it"
msgstr "La diapositiva se puede marcar como completada incluso sin abrirla"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__can_self_mark_uncompleted
msgid "The slide can be marked as not completed and the progression"
msgstr "La diapositiva se puede marcar como no completada y el progreso"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"The video link to input here can be obtained by using the 'Share link' "
"button in the Vimeo interface."
msgstr ""
"Puede obtener el enlace del vídeo a introducir aquí mediante el botón de "
"\"Compartir enlace\" en la interfaz de Vimeo."

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_partner_check_vote
msgid "The vote must be 1, 0 or -1."
msgstr "El voto debe ser 1, 0 o -1."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Then, go into the file permissions and set it as \"Anyone with the link\"."
msgstr ""
"Después, vaya a los permisos del archivo y seleccione \"Cualquier persona "
"con el enlace\"."

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_theory
msgid "Theory"
msgstr "Teoría"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "There are no comments for now."
msgstr "No hay comentarios por ahora."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"There are no comments for now. Earn more Karma to be the first to leave a "
"comment."
msgstr ""
"Todavía no hay comentarios. Obtenga más karma para ser el primero en "
"comentar."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "There was an error validating this quiz."
msgstr "Hubo un error validando este cuestionario."

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__url
msgid "Third Party Website URL"
msgstr "URL de sitio web de terceros"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Third Try"
msgstr "Tercer intento"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_answer__comment
msgid "This comment will be displayed to the user if they select this answer"
msgstr "Este comentario se mostrará al usuario si seleccionan esta respuesta"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "This course does not exist."
msgstr "Este curso no existe."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid ""
"This course is not published. Attendees may not be able to access its "
"contents."
msgstr ""
"Este curso no está publicado. Es posible que los asistentes no puedan "
"acceder al contenido. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "This course is private."
msgstr "Este curso es privado."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "This identification link does not seem to be valid."
msgstr "Parece que el enlace de identificación no es válido. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "This invitation link has an invalid hash."
msgstr "Este enlace de invitación tiene un hash no válido. "

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "This invitation link has expired."
msgstr "Este enlace de invitación venció."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "This invitation link is not for this contact."
msgstr "Este enlace de invitación no es para este contacto. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "This is the correct answer"
msgstr "Esta es la respuesta correcta."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "This is the correct answer, congratulations"
msgstr "Esta es la respuesta correcta, felicidades"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_question.py:0
#, python-format
msgid ""
"This question must have at least one correct answer and one incorrect "
"answer."
msgstr ""
"Esta pregunta debe tener al menos una respuesta correcta y una incorrecta."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "This quiz is already done. Retaking it is not possible."
msgstr "Este cuestionario ya está hecho. No es posible repetirlo."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "This slide can not be marked as completed."
msgstr "Esta diapositiva no se puede marcar como completada."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "This slide can not be marked as uncompleted."
msgstr "Esta diapositiva no se puede marcar como no completada."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "This video already exists in this channel on the following content: %s"
msgstr "Este vídeo ya existe en este canal, en el siguiente contenido: %s"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"Through Google Drive, we support most common types of documents.\n"
"                                        Including regular documents (Google Doc, .docx), Sheets (Google Sheet, .xlsx), PowerPoints, ..."
msgstr ""
"Con Google Drive, somos compatibles con la mayoría de los tipos más comunes de documentos.\n"
"                                        Esto incluye documentos regulares (Google Doc, .docx), hojas de cálculo (Google Sheet, .xlsx), presentaciones, ..."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__name
#, python-format
msgid "Title"
msgstr "Título"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Toggle navigation"
msgstr "Alternar navegación"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_tools
msgid "Tools"
msgstr "Herramientas"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_0
msgid "Tools and Methods"
msgstr "Herramientas y Métodos"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_0
msgid "Tools you will need to complete this course."
msgstr "Herramientas que necesitará para completar este curso."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Total"
msgstr "Total"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "Total Attendees"
msgstr "Total de asistentes"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "Total Completed"
msgstr "Total completado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "Total Duration"
msgstr "Duración total"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "Total Questions"
msgstr "Total de preguntas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_slides
msgid "Total Slides"
msgstr "Total de diapositivas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "Total Views"
msgstr "Total de vistas"

#. module: website_slides
#: model:ir.model,name:website_slides.model_gamification_karma_tracking
msgid "Track Karma Changes"
msgstr "Seguimiento de cambios de karma"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__training
msgid "Training"
msgstr "Formación"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_0
msgid "Tree Infographic"
msgstr "Infografía de árbol"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_2
msgid "Tree planting in hanging bottles on wall"
msgstr "Plantación de árboles en botellas colgadas en la pared"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_0
msgid "Trees"
msgstr "Árboles"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_2_gard2
msgid "Trees, Wood and Gardens"
msgstr "Árboles, Madera y Jardines"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Type"
msgstr "Tipo"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción en el registro."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__url
msgid "URL of the Google Drive file or URL of the YouTube video"
msgstr "URL del archivo de Google Drive o del vídeo de YouTube"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr ""
"No se puede publicar el mensaje, configure la dirección de correo "
"electrónico del remitente."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Unarchive"
msgstr "Desarchivar"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#, python-format
msgid "Uncategorized"
msgstr "Sin categoría"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_0
msgid "Unforgettable Tools"
msgstr "Herramientas que no debe olvidar"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_embed.py:0
#, python-format
msgid "Unknown Website"
msgstr "Sitio web desconocido"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Unknown error"
msgstr "Error desconocido"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#, python-format
msgid "Unknown error, try again."
msgstr "Error desconocido, vuelva a intentarlo."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Unlisted"
msgstr "No listado"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Unlisted (paid account)"
msgstr "No listado (cuenta pagada)"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "Unpublished"
msgstr "No publicado"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "Update"
msgstr "Actualizar"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Update all your Attendees at once through mass mailings."
msgstr ""
"Actualice todos sus asistentes a la vez a través del envío masivo de "
"correos."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "Upload Document"
msgstr "Subir documento"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__upload_group_ids
msgid "Upload Groups"
msgstr "Subir grupos"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__source_type__local_file
#, python-format
msgid "Upload from Device"
msgstr "Subir desde el dispositivo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_id
msgid "Uploaded by"
msgstr "Subido por"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "Uploading document ..."
msgstr "Subiendo el documento..."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.action_slide_tag
msgid "Use Content Tags to classify your Content."
msgstr "Utilice las etiquetas de contenido para clasificar su contenido."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_tag_group_action
msgid "Use Course Groups to classify and organize your Courses."
msgstr "Utilice grupos de cursos para clasificar y organizar sus cursos."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Use template"
msgstr "Usar plantilla"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__tag_ids
msgid "Used to categorize and filter displayed channels/courses"
msgstr "Usado para categorizar y filtrar canales y cursos mostrados"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__color
msgid "Used to decorate kanban view"
msgstr "Usado para decorar la vista kanban"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_users
msgid "User"
msgstr "Usuario"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_vote
msgid "User vote"
msgstr "Votos de usuarios"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Validation error"
msgstr "Error de validación"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__video
#, python-format
msgid "Video"
msgstr "Vídeo"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__video_url
#, python-format
msgid "Video Link"
msgstr "Enlace de vídeo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__video_source_type
msgid "Video Source"
msgstr "Origen del vídeo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__vimeo_id
msgid "Video Vimeo ID"
msgstr "ID del vídeo de Vimeo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__youtube_id
msgid "Video YouTube ID"
msgstr "ID del vídeo de YouTube"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_video
msgid "Videos"
msgstr "Vídeos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "View"
msgstr "Ver"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "View all"
msgstr "Ver todo"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "View course"
msgstr "Ver curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Views"
msgstr "Vistas"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_card
msgid "Views •"
msgstr "Vistas •"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__video_source_type__vimeo
#, python-format
msgid "Vimeo"
msgstr "Vimeo"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__vimeo_video
msgid "Vimeo Video"
msgstr "Vídeo de Vimeo"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_published
msgid "Visible on current website"
msgstr "Visible en el sitio web actual"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Visits"
msgstr "Visitas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_vote
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__vote
msgid "Vote"
msgstr "Voto"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_votes
msgid "Votes"
msgstr "Votos"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "Votes and comments are disabled for this course"
msgstr "Votaciones y comentarios han sido deshabilitados para este curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Waiting for validation"
msgstr "Esperando validación"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "Want to test and certify your students?"
msgstr "¿Quiere realizar pruebas y certificar a sus estudiantes?"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_1
msgid "Watching the master(s) at work"
msgstr "Observe cómo lo hacen los expertos"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_4
msgid ""
"We had a little chat with Harry Potted, sure he had interesting things to "
"say!"
msgstr ""
"¡Charlamos un poco con Harry Potted, seguro que tenía cosas interesantes que"
" decir!"

#. module: website_slides
#: model:ir.model,name:website_slides.model_website
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__website_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_id
msgid "Website"
msgstr "Sitio web"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__gamification_challenge__challenge_category__slides
msgid "Website / Slides"
msgstr "Sitio web / Diapositivas"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_url
msgid "Website URL"
msgstr "URL del sitio web"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_description
msgid "Website meta description"
msgstr "Descripción meta del sitio web"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_keywords
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_keywords
msgid "Website meta keywords"
msgstr "Palabras clave meta del sitio web"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_title
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_title
msgid "Website meta title"
msgstr "Título meta del sitio web"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_og_img
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_og_img
msgid "Website opengraph image"
msgstr "Imagen Open Graph del sitio web"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Welcome on your course's home page. It's still empty for now. Click on "
"\"<b>New</b>\" to write your first course."
msgstr ""
"Bienvenido a la página de inicio de su curso. Sigue vacío por ahora. Haga "
"clic en \"<b>Nuevo</b>\" para escribir su primer curso."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "What does"
msgstr "¿Qué significa"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_0
msgid "What is a strawberry?"
msgstr "¿Qué es una fresa?"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_1
msgid "What is the best tool to dig a hole for your plants?"
msgstr "¿Cuál es la mejor herramienta para cavar un hoyo para sus plantas?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "What types of documents do we support?"
msgstr "¿Qué tipos de documentos son compatibles?"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_2
msgid "What was the question again?"
msgstr "¿Puede repetir la pregunta?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "When using local files, we only support PDF files."
msgstr "Al usar archivos locales, solo aceptamos archivos PDF."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__enroll_mode
msgid ""
"Whether invited partners will be added as enrolled. Otherwise, they will be "
"added as invited."
msgstr ""
"Los contactos invitados se añadirán como inscritos. De lo contrario, se "
"añadirán como invitados. "

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_1
msgid ""
"Which wood type is best for my solid wood furniture? That's the question we "
"help you answer in this video!"
msgstr ""
"¿Qué tipo de madera es mejor para mi mueble de madera sólida? ¡Esa es la "
"pregunta que le ayudaremos a contestar en este vídeo!"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"With Quizzes you can keep your students focused and motivated by answering "
"some questions and gaining some karma points"
msgstr ""
"Con los cuestionarios puede hacer que sus estudiantes se mantengan "
"concentrados y motivados al responder algunas preguntas y ganar algunos "
"puntos de karma."

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_1
msgid "Wood"
msgstr "Madera"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_1
msgid "Wood Bending With Steam Box"
msgstr "Doblado de madera con caja de vapor"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_3
msgid "Wood Characteristics"
msgstr "Características de la madera"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_1
msgid "Wood Types"
msgstr "Tipos de madera"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_3_0
msgid "Working with Wood"
msgstr "Trabajar con madera"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product or services. <br>To be "
"successful your content needs to be useful to your readers."
msgstr ""
"Escriba uno o dos párrafos que describan su producto o servicios. <br>Para "
"tener éxito, su contenido debe ser útil para sus lectores."

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br> To be successful your content needs to be useful to your "
"readers."
msgstr ""
"Escriba uno o dos párrafos que describan su producto, servicios o una "
"característica específica.<br> Para tener éxito, su contenido debe ser útil "
"para sus lectores."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "XP"
msgstr "XP"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_0
#, python-format
msgid "Yes"
msgstr "Sí"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid ""
"You are not allowed to add members to this course. Please contact the course"
" responsible or an administrator."
msgstr ""
"No tiene permitido añadir miembros a este curso. Contacte al responsable del"
" curso o a un administrador."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"You can add <b>comments</b> on answers. This will be visible with the "
"results if the user select this answer."
msgstr ""
"Puede añadir <b>comentarios</b> en las respuestas. Esto será visible con los"
" resultados si el usuario selecciona esta respuesta"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "You can add questions to this quiz in the 'Quiz' tab."
msgstr ""
"Puede añadir preguntas a este cuestionario en la pestaña de "
"\"Cuestionario\"."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"You can either upload a file from your computer or insert a Google Drive "
"link."
msgstr ""
"Puede subir un archivo de su ordenador o insertar un enlace de Google Drive."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_upload.js:0
#, python-format
msgid "You can not upload password protected file."
msgstr "No puede subir un archivo protegido con contraseña."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "You cannot add tags to this course."
msgstr "No puede añadir etiquetas a este curso."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "You cannot mark a slide as completed if you are not among its members."
msgstr "No puede marcar una diapositiva como completada si no es un miembro."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"You cannot mark a slide as uncompleted if you are not among its members."
msgstr "No puede marcar una diapositiva como no completada si no es miembro."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid "You cannot mark a slide as viewed if you are not among its members."
msgstr "No puede marcar una diapositiva como vista si no es un miembro."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"You cannot mark a slide quiz as completed if you are not among its members "
"or it is unpublished."
msgstr ""
"No puede marcar un cuestionario de diapositiva como completado si no es "
"parte de sus miembros o si no está publicado."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"You cannot mark a slide quiz as not completed if you are not among its "
"members or it is unpublished."
msgstr ""
"No puede marcar un cuestionario de diapositiva como no completado si no es "
"parte de sus miembros o si no está publicado."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "You cannot upload on this channel."
msgstr "No puede subir contenido a este canal."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "You did it!"
msgstr "¡Lo logró!"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#, python-format
msgid "You do not have permission to access this course."
msgstr "No tiene permiso para entrar a este curso. "

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You don't have access to this lesson"
msgstr "No tiene acceso a esta lección"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You don't have enough karma to vote"
msgstr "No tiene suficiente karma para votar"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "You gained"
msgstr "Ha ganado"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#, python-format
msgid "You have already joined this channel"
msgstr "Ya se ha unido a este canal"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_slide_channel_invite
msgid "You have been invited to check out {{ object.channel_id.name }}"
msgstr "Ha sido invitado a ver {{ object.channel_id.name }}"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_slide_channel_enroll
msgid "You have been invited to join {{ object.channel_id.name }}"
msgstr "Ha sido invitado a unirse a  {{ object.channel_id.name }}"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "You have been invited to this course."
msgstr "Ha sido invitado a este curso."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "You have to sign in before"
msgstr "Tiene que iniciar sesión antes"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "You may now participate in our eLearning."
msgstr "Puede participar en nuestro eLearning."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#, python-format
msgid "You must be logged to submit the quiz."
msgstr "Debe iniciar sesión para enviar el cuestionario."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
#, python-format
msgid "You must be member of this course to vote"
msgstr "Necesita ser miembro del curso para votar"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "You need to join this course to access \""
msgstr "Debe unirse a este curso para acceder a \""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_2
msgid "You won't believe those facts about carrots."
msgstr "No creerá estos datos sobre las zanahorias."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "You're enrolled"
msgstr "Está inscrito"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__video_source_type__youtube
#, python-format
msgid "YouTube"
msgstr "YouTube"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__youtube_video
msgid "YouTube Video"
msgstr "Vídeo de YouTube"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Your"
msgstr "Su"

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_level
msgid "Your Level"
msgstr "Su nivel"

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_role
msgid "Your Role"
msgstr "Su rol"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_overview
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_report
msgid ""
"Your eLearning platform starts here!<br>\n"
"                    Upload content, set up rewards, manage attendees..."
msgstr ""
"¡Su plataforma de eLearning inicia aquí!<br>\n"
"                    Suba contenido, configure recompensas, gestione asistentes..."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Your file could not be found on Google Drive, please check the link and/or "
"privacy settings"
msgstr ""
"No se encontró su archivo en Google Drive, compruebe el enlace y/o los "
"ajustes de privacidad"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
#, python-format
msgid ""
"Your first section is created, now it's time to add lessons to your course. "
"Click on <b>Add Content</b> to upload a document, create an article or link "
"a video."
msgstr ""
"Se creó su primera sección, ahora es momento de añadir lecciones a su curso."
" Haga clic en <b>Añadir contenido</b> para subir un documento, crear un "
"artículo o vincular un vídeo."

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Your video could not be found on Vimeo, please check the link and/or privacy"
" settings"
msgstr ""
"No se encontró su vídeo en Vimeo, compruebe el enlace y/o los ajustes de "
"privacidad"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#: code:addons/website_slides/models/slide_slide.py:0
#, python-format
msgid ""
"Your video could not be found on YouTube, please check the link and/or "
"privacy settings"
msgstr ""
"No se encontró su vídeo en YouTube, compruebe el enlace y/o los ajustes de "
"privacidad"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_channel.py:0
#, python-format
msgid "a course"
msgstr "un curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.achievement_card
msgid "achieved"
msgstr "logrado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "and join this Community"
msgstr "y unáse a esta comunidad"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "anyway"
msgstr "de cualquier forma"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "app."
msgstr "."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "breadcrumb"
msgstr "barra de migas"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "by email."
msgstr "por correo electrónico."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "courses"
msgstr "cursos"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "create an account"
msgstr "crear una cuenta"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "direct access"
msgstr "acceso directo"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#, python-format
msgid "e.g \"https://drive.google.com/file/...\""
msgstr "p. ej. \"https://drive.google.com/file/...\""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "e.g \"https://www.youtube.com/watch?v=ebBez6bcSEc\""
msgstr "p. ej. \"https://www.youtube.com/watch?v=ebBez6bcSEc\""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g \"www.youtube.com/watch?v=ebBez6bcSEc\""
msgstr "p. ej. \"www.youtube.com/watch?v=ebBez6bcSEc\""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
msgid "e.g 'HowTo'"
msgstr "p. ej. \"Cómo...\""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "e.g. \"15\""
msgstr "p. ej. \"15\""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "e.g. \"Computer Science for kids\""
msgstr "p. ej. \"Informática para niños\""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/category_add_dialog/category_add_dialog.xml:0
#, python-format
msgid "e.g. \"Introduction\""
msgstr "p. ej. \"Introducción\""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#, python-format
msgid "e.g. \"Which animal cannot fly?\""
msgstr "p. ej. \"¿Qué animal no puede volar?\""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid "e.g. Computer Science for kids"
msgstr "ej. Informática para niños"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid ""
"e.g. In this video, we'll give you the keys on how Odoo can help you to grow"
" your business. At the end, we'll propose you a quiz to test your knowledge."
msgstr ""
"Por ejemplo, en este vídeo le mostraremos la clave de cómo Odoo puede "
"ayudarle a hacer crecer su negocio. Al final, le propondremos un "
"cuestionario para poner a prueba sus conocimientos."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. Setting up your computer"
msgstr "p. ej. \"Configurar su ordenador\""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "e.g. What powers a computer?"
msgstr "p. ej. \"¿Qué le da energía a un ordenador?\""

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "e.g. Your Level"
msgstr "p. ej. Su nivel"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_root
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "eLearning"
msgstr "eLearning"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_ids
msgid "eLearning Courses"
msgstr "Cursos de eLearning"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "eLearning Overview"
msgstr "vista general de eLearning"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "for 'Private' videos and similar to"
msgstr "para vídeos \"Privados\" y similar a"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "for public ones."
msgstr "para públicos."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#, python-format
msgid "<EMAIL>, <EMAIL>"
msgstr "<EMAIL>, <EMAIL>"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "https://drive.google.com/file/d/ABC/view?usp=sharing"
msgstr "https://drive.google.com/file/d/ABC/view?usp=sharing"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "https://vimeo.com/558907333/30da9ff3d8"
msgstr "https://vimeo.com/558907333/30da9ff3d8"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "https://vimeo.com/558907555"
msgstr "https://vimeo.com/558907555"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "join"
msgstr "únase a"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "login"
msgstr "inicíe sesión"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"mean? The Vimeo \"Unlisted\" privacy setting means it is a video which can be viewed only by the users with the link to it.\n"
"                                    Your video will never come up in the search results nor on your channel."
msgstr ""
"? La configuración de privacidad de Vimeo \"No listado\" hace que solo los usuarios que tienen un enlace puedan verlo.\n"
"                                    ? El vídeo no aparecerá dentro de los resultados de búsqueda o en su canal."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid ""
"means? The YouTube \"unlisted\" means it is a video which can be viewed only"
" by the users with the link to it. Your video will never come up in the "
"search results nor on your channel."
msgstr ""
"significa? El \"No listado\" de YouTube significa que es un video que puede "
"ser visto solamente por usuarios que tengan el enlace. Su video jamás "
"aparecerá en los resultados de búsqueda o en su canal."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#, python-format
msgid "or"
msgstr "o/u"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
#, python-format
msgid "or Leave the course"
msgstr "o abandonar el curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "request"
msgstr "solicite"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#, python-format
msgid "sign in"
msgstr "inicíe sesión"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "start"
msgstr "comience"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid "steps"
msgstr "pasos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
msgid "to access resources"
msgstr "para acceder a recursos"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "to be the first to leave a comment."
msgstr "para ser el primero en comentar."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "to browse preview content and enroll."
msgstr "para buscar contenido previo e inscribirse."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "to contact responsible"
msgstr "para contactar un responsable"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#, python-format
msgid "to enroll."
msgstr "para inscribirse."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#, python-format
msgid "to join this course"
msgstr "para unirse a este curso"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "to request access"
msgstr "para solicitar acceso"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_share.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
#, python-format
msgid "to share this"
msgstr "para compartir este"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "to unlock"
msgstr "para desbloquear"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#: code:addons/website_slides/static/src/xml/website_slides_upload.xml:0
#, python-format
msgid "unlisted"
msgstr "no listado"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "xp"
msgstr "xp"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<EMAIL>, <EMAIL>"
msgstr "<EMAIL>, <EMAIL>"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_channel_shared
msgid "{{ user.name }} shared a Course"
msgstr "{{ user.name }} compartió un curso"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_shared
msgid "{{ user.name }} shared a {{ object.slide_category }} with you!"
msgstr "¡ {{ user.name }} compartió {{ object.slide_category }} con usted!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "└<span class=\"ms-1\">Uncategorized</span>"
msgstr "└<span class=\"ms-1\">Sin categorizar</span>"
