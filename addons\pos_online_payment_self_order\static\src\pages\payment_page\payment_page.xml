<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="pos_online_payment_self_order.PaymentPage" t-inherit="pos_self_order.PaymentPage" t-inherit-mode="extension">
        <xpath expr="//div[hasclass('payment-state-container')]" position="after">
            <div t-if="this.selectedPaymentIsOnline and !state.selection and selfOrder.config.self_ordering_mode === 'kiosk'" class="d-flex justify-content-center align-items-center flex-column h-100 px-3 text-center">
                <h1 class="mb-4">Scan the QR code to pay</h1>
                <div class="d-inline-flex flex-column border rounded p-4 bg-view mb-3">
                    <img t-att-src="state.qrImage" />
                </div>
                <h3 t-if="selfOrder.onlinePaymentStatus === 'progress'">Payment in progress</h3>
            </div>
        </xpath>
        <xpath expr="//div[hasclass('payment-state-container')]" position="attributes">
            <attribute name="t-if">!this.selectedPaymentIsOnline and !state.selection</attribute>
        </xpath>
    </t>
</templates>
