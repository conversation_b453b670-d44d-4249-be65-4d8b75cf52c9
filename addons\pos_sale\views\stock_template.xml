<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="message_body" inherit_id="stock.message_body">
        <xpath expr="//t[contains(@t-if, 'move.state')]" position="inside">
            <t groups="point_of_sale.group_pos_user">
                <t t-set="pos_orders" t-value="move.sale_line_id.pos_order_line_ids.mapped('order_id')" />
                <t t-if="len(pos_orders)">
                    <li>
                        Delivered from
                        <t t-foreach="pos_orders" t-as="pos_order">
                            <a href="#" t-att-data-oe-model="pos_order._name" t-att-data-oe-id="pos_order.id">
                                <t t-esc="pos_order.display_name" />
                            </a><span t-if="pos_orders.ids[-1:] != pos_order.ids">, </span>
                        </t>
                    </li>
                </t>
            </t>
        </xpath>
    </template>
</odoo>
