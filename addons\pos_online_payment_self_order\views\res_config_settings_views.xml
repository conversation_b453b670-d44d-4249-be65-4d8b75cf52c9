<?xml version="1.0"?>
<odoo>
    <record id="res_config_settings_view_form_menu" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.pos_online_payment.view</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="point_of_sale.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='self-payment-after']" position="after">
                <div class="content-group row" invisible="not pos_self_ordering_mode == 'mobile'">
                    <label string="Online Payment" for="pos_self_order_online_payment_method_id" class="col-lg-4" />
                    <field name="pos_self_order_online_payment_method_id" placeholder="Pay at cashier if empty" />
                </div>
                <div class="d-flex flex-column align-items-start w-50" invisible="not pos_self_ordering_mode == 'mobile' and not pos_self_ordering_pay_after == 'each'">
                    <button name="%(point_of_sale.action_payment_methods_tree)d" icon="oi-arrow-right" type="action" string="Payment Methods" class="btn-link" />
                </div>
            </xpath>
        </field>
    </record>
</odoo>
