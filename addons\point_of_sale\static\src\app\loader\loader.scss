.pos-loader {
    background-color: #222;
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    z-index: 100001; // 1 more than the debug widget...
    text-align: center;
    font-family: Lato;
    color: $gray-700;
    transition: opacity 0.8s;

    .loader-feedback {
        width: 400px;
        height: 160px;
        margin: -60px -200px;
        position: absolute;
        left: 50%;
        top: 50%;
        text-align: center;
    }

    h1 {
        font-weight: 300;
    }

    .button {
        display: inline-block;
        margin: 25px auto;
        line-height: 42px;
        padding: 0px 16px;
        font-size: 20px;
        font-weight: 300;
        border: solid 1px;
        border-radius: 5px;
        cursor: pointer;

        :active {
            color: #222;
            background: $gray-700;
        }
    }
}

// loader animation
.lds-ellipsis {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 60px;
}
.lds-ellipsis div {
    position: absolute;
    top: 33px;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: #fff;
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}
.lds-ellipsis div:nth-child(1) {
    left: 8px;
    animation: lds-ellipsis1 0.6s infinite;
}
.lds-ellipsis div:nth-child(2) {
    left: 8px;
    animation: lds-ellipsis2 0.6s infinite;
}
.lds-ellipsis div:nth-child(3) {
    left: 32px;
    animation: lds-ellipsis2 0.6s infinite;
}
.lds-ellipsis div:nth-child(4) {
    left: 56px;
    animation: lds-ellipsis3 0.6s infinite;
}
@keyframes lds-ellipsis1 {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
}
@keyframes lds-ellipsis3 {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(0);
    }
}
@keyframes lds-ellipsis2 {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(24px, 0);
    }
}
