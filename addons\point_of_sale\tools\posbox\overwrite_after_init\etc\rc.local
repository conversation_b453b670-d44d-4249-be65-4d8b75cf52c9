#!/bin/sh -e
#
# rc.local
#
# This script is executed at the end of each multiuser runlevel.
# Make sure that the script will "exit 0" on success or any other
# value on error.
#
# In order to enable or disable this script just change the execution
# bits.
#
# By default this script does nothing.

# Print the IP address
_IP=$(hostname -I) || true
if [ "$_IP" ]; then
  printf "My IP address is %s\n" "$_IP"
fi

mkdir -p /var/run/odoo
chown pi:pi /var/run/odoo

/home/<USER>/odoo/addons/point_of_sale/tools/posbox/configuration/wireless_ap.sh &

exit 0
